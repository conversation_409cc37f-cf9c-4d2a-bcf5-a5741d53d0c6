
import { OnboardingData, ProjectConfig, SubmissionStatus } from './types';

// Create default empty form data
export const defaultFormData: OnboardingData = {
  firstName: '',
  lastName: '',
  email: '',
  dob: '',
  address: '',
  city: '',
  state: '',
  zip: '',
  
  jobTitle: '',
  department: '',
  manager: '',
  startDate: '',
  
  workEmail: '',
  needsM365: true,
  deviceType: '',
  phoneType: '',
  intuneGroup: '',
  additionalSoftware: [], // Initialize as empty array
  peripherals: [], // Also initialize peripherals array
  customAccessories: '',
  
  submittedBy: '',
  submittedAt: null,
  status: 'draft',
  processedBy: null,
  processedAt: null,
};

// Create default project configuration
export const defaultProjectConfig: ProjectConfig = {
  useEntraID: true,
  useM365: true,
  useIntune: true,
  useTeams: true,
  useSlack: false,
  useHRSystem: false,
  useDynamicGroups: false,
  useCustomFields: false,
};

// Mock submissions for demo purposes
export const mockSubmissions: OnboardingData[] = [
  {
    ...defaultFormData,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    jobTitle: 'Marketing Specialist',
    department: 'Marketing',
    manager: 'Jane Smith',
    startDate: '2025-05-01',
    workEmail: '<EMAIL>',
    submittedBy: '<EMAIL>',
    submittedAt: '2025-04-05T14:48:00',
    status: 'submitted'
  },
  {
    ...defaultFormData,
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    jobTitle: 'Software Engineer',
    department: 'Engineering',
    manager: 'Mike Chen',
    startDate: '2025-04-20',
    workEmail: '<EMAIL>',
    submittedBy: '<EMAIL>',
    submittedAt: '2025-04-01T09:30:00',
    status: 'in_progress',
    processedBy: '<EMAIL>',
    processedAt: '2025-04-02T11:15:00'
  }
];

export const sampleSubmissions: Submission[] = [
  {
    id: "1",
    firstName: "John",
    lastName: "Smith",
    email: "<EMAIL>",
    workEmail: "<EMAIL>",
    personalEmail: "<EMAIL>",
    phoneNumber: "(*************",
    address: "123 Main Street",
    city: "Springfield",
    state: "IL",
    zip: "62701",
    startDate: "2024-03-01",
    department: "Engineering",
    jobTitle: "Senior Software Developer",
    jobCategory: "Technology",
    branch: "Headquarters",
    physicalWorkLocation: "branch",
    physicalWorkAddress: "456 Corporate Plaza, Springfield, IL 62701",
    hiringManagerFirstName: "Sarah",
    hiringManagerLastName: "Johnson",
    hiringManagerEmail: "<EMAIL>",
    isRehire: false,
    hireType: "newHire",
    positionType: "replacement",
    replacedFirstName: "Michael",
    replacedLastName: "Brown",
    workSchedule: "Monday-Friday, 9:00 AM - 5:00 PM EST",
    hrNotes: "Requires parking pass and building access card",
    deviceType: "laptop",
    phoneType: "iphone",
    needsM365: true,
    deskPhoneExt: "4567",
    monitorQuantity: 2,
    shipToAddress: "456 Corporate Plaza, Springfield, IL 62701",
    itDevices: ["laptop", "deskPhone", "monitors"],
    itNotes: "Requires local admin access",
    status: "pending",
    createdAt: "2024-02-20",
    updatedAt: "2024-02-20"
  },
  {
    id: "2",
    firstName: "Jane",
    lastName: "Doe",
    email: "<EMAIL>",
    workEmail: "<EMAIL>",
    personalEmail: "<EMAIL>",
    phoneNumber: "(*************",
    address: "789 Oak Road",
    city: "Boston",
    state: "MA",
    zip: "02108",
    startDate: "2024-03-15",
    department: "Marketing",
    jobTitle: "Marketing Manager",
    jobCategory: "Marketing",
    branch: "East Coast",
    physicalWorkLocation: "remote",
    physicalWorkAddress: "789 Oak Road, Boston, MA 02108",
    hiringManagerFirstName: "Mike",
    hiringManagerLastName: "Wilson",
    hiringManagerEmail: "<EMAIL>",
    isRehire: true,
    hireType: "rehire",
    positionType: "new",
    workSchedule: "Monday-Friday, 8:00 AM - 4:00 PM EST",
    hrNotes: "Previous employee returning after 2 years",
    deviceType: "desktop",
    phoneType: "android",
    needsM365: true,
    deskPhoneExt: "5678",
    monitorQuantity: 1,
    shipToAddress: "789 Oak Road, Boston, MA 02108",
    itDevices: ["desktop", "headset"],
    itNotes: "Standard setup required",
    status: "submitted",
    createdAt: "2024-02-21",
    updatedAt: "2024-02-21"
  }
];




