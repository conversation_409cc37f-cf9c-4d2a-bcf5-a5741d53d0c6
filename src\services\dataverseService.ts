/**
 * Service for interacting with the Dataverse API through Azure Functions
 */

import { getAuthHeaders } from '../utils/authUtils';
import { OnboardingData } from '../context/types';

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * Validate employee form data before submission
 * @param formData Form data to validate
 * @returns ValidationResult Validation result
 */
export function validateEmployeeData(formData: OnboardingData): ValidationResult {
  const errors: ValidationError[] = [];

  // Validate personal information
  if (!formData.firstName || formData.firstName.trim() === '') {
    errors.push({ field: 'firstName', message: 'First name is required' });
  }

  if (!formData.lastName || formData.lastName.trim() === '') {
    errors.push({ field: 'lastName', message: 'Last name is required' });
  }

  if (!formData.email || formData.email.trim() === '') {
    errors.push({ field: 'email', message: 'Email is required' });
  } else if (!isValidEmail(formData.email)) {
    errors.push({ field: 'email', message: 'Email is not valid' });
  }

  // Validate job information
  if (!formData.jobTitle || formData.jobTitle.trim() === '') {
    errors.push({ field: 'jobTitle', message: 'Job title is required' });
  }

  if (!formData.department || formData.department.trim() === '') {
    errors.push({ field: 'department', message: 'Department is required' });
  }

  if (!formData.startDate || formData.startDate.trim() === '') {
    errors.push({ field: 'startDate', message: 'Start date is required' });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate email address
 * @param email Email to validate
 * @returns boolean True if valid, false otherwise
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Submit employee form data to Dataverse through Azure Function
 * @param formData Form data to submit
 * @returns Promise<any> Response from the Azure Function
 */
export async function submitToDataverse(formData: OnboardingData): Promise<any> {
  try {
    // Validate the form data client-side first
    const validationResult = validateEmployeeData(formData);
    if (!validationResult.valid) {
      throw new Error(`Validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`);
    }

    // Get the API base URL from environment variables
    const apiBaseUrl = import.meta.env.VITE_FUNC_ENDPOINT || '';

    // Get the authorization token
    const token = await getTokenForApi('DATAVERSE');

    // Prepare the data for submission
    const submissionData = {
      ...formData,
      submittedAt: new Date().toISOString(),
      status: 'submitted'
    };

    // Submit the form data
    const response = await fetch(`${apiBaseUrl}/api/submitEmployeeForm`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(submissionData)
    });

    // Check if the request was successful
    if (!response.ok) {
      const errorData = await response.json();
      if (errorData.validationErrors) {
        // Server-side validation failed
        throw new Error(`Server validation failed: ${errorData.validationErrors.map(e => e.message).join(', ')}`);
      }
      throw new Error(errorData.error || `Error: ${response.status} ${response.statusText}`);
    }

    // Return the response data
    return await response.json();
  } catch (error) {
    console.error('Error submitting to Dataverse:', error);
    throw error;
  }
}

export class DataverseService {
  private static async fetchWithAuth(endpoint: string, options: RequestInit = {}, timeoutMs: number = 10000) {
    try {
      const headers = await getAuthHeaders('DATAVERSE');
      const baseUrl = import.meta.env.VITE_FUNC_ENDPOINT;

      console.log(`DataverseService: Fetching from ${baseUrl}/api/${endpoint}`);
      
      // Log the headers (but not the full auth token)
      const headerKeys = Object.keys(headers);
      console.log(`DataverseService: Request headers: ${headerKeys.join(', ')}`);
      
      if (!baseUrl) {
        console.error('DataverseService: VITE_FUNC_ENDPOINT is not defined');
        throw new Error('API endpoint URL is not configured');
      }

      // Create an AbortController to handle timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

      const response = await fetch(`${baseUrl}/api/${endpoint}`, {
        ...options,
        headers: {
          ...headers,
          ...options.headers
        },
        signal: controller.signal
      });

      // Clear the timeout since we got a response
      clearTimeout(timeoutId);

      console.log(`DataverseService: Received response with status ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`DataverseService: Error response (${response.status}): ${errorText}`);
        
        let errorObj;
        try {
          errorObj = JSON.parse(errorText);
        } catch (e) {
          errorObj = { message: errorText };
        }
        throw new Error(errorObj.message || `API error: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error.name === 'AbortError') {
        console.error(`DataverseService: Request to ${endpoint} timed out after ${timeoutMs}ms`);
        throw new Error(`Request timed out after ${timeoutMs}ms`);
      }
      
      // Log detailed error information
      console.error(`DataverseService: Error in fetchWithAuth for ${endpoint}:`, error);
      console.error('DataverseService: Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
      
      throw error;
    }
  }

  static async createEmployee(employeeData: any) {
    return this.fetchWithAuth('createEmployee', {
      method: 'POST',
      body: JSON.stringify(employeeData)
    });
  }

  static async getEmployees() {
    try {
      console.log('DataverseService: Fetching employees from API...');
      const response = await this.fetchWithAuth('getEmployees');
      console.log('DataverseService: Raw API response:', response);
      
      // Add more detailed logging about the response structure
      if (response && typeof response === 'object') {
        console.log('DataverseService: Response keys:', Object.keys(response));
        
        if (response.employees) {
          console.log(`DataverseService: Found ${response.employees.length} employees in response`);
          
          // If array is empty, log possible reasons
          if (response.employees.length === 0) {
            console.log('DataverseService: Employee array is empty. Possible reasons:');
            console.log('1. No employees exist in the Dataverse database');
            console.log('2. The API endpoint is not returning data correctly');
            console.log('3. There might be permission issues accessing the data');
          }
        } else {
          console.log('DataverseService: No employees property found in response');
          console.log('DataverseService: Full response:', response);
        }
      }
      
      return response;
    } catch (error) {
      console.error('DataverseService: Error fetching employees:', error);
      // Return an empty array as fallback
      return { employees: [] };
    }
  }

  static async getEmployeeById(id: string) {
    return this.fetchWithAuth(`employee/${id}`);
  }

  static async getSubmissions() {
    console.log('DataverseService: getSubmissions called');
    try {
      console.log('DataverseService: Attempting to fetch submissions');
      const response = await this.fetchWithAuth('getSubmissions', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      }, 20000); // Increase timeout to 20 seconds
      
      console.log('DataverseService: Raw response received:', response);
      
      // Add defensive checks
      if (!response) {
        console.error('DataverseService: Empty response received');
        return [];
      }
      
      // Check if response has the expected structure
      if (!Array.isArray(response.submissions)) {
        console.error('DataverseService: Unexpected response structure:', response);
        return [];
      }
      
      console.log(`DataverseService: Successfully retrieved ${response.submissions.length} submissions`);
      return response.submissions;
    } catch (error) {
      console.error('DataverseService: Error in getSubmissions:', error);
      console.error('DataverseService: Error stack:', error.stack);
      // Return empty array instead of throwing to prevent UI crashes
      return [];
    }
  }
}





