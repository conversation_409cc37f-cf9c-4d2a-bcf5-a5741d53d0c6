
import React, { useState } from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Laptop } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

const ITSetupStep: React.FC = () => {
  const { formData, updateFormData, currentRole, projectConfig } = useOnboarding();

  // Add state for shipping address type
  const [shippingAddressType, setShippingAddressType] = useState("personal");

  // Ensure additionalSoftware exists
  const additionalSoftware = formData.additionalSoftware || [];
  
  const handleSoftwareChange = (software: string) => {
    const currentSoftware = [...additionalSoftware];
    
    if (currentSoftware.includes(software)) {
      updateFormData('additionalSoftware', currentSoftware.filter(sw => sw !== software));
    } else {
      updateFormData('additionalSoftware', [...currentSoftware, software]);
    }
  };

  const handlePeripheralChange = (peripheral: string) => {
    const currentPeripherals = [...(formData.peripherals || [])];
    
    if (currentPeripherals.includes(peripheral)) {
      updateFormData('peripherals', currentPeripherals.filter(p => p !== peripheral));
    } else {
      updateFormData('peripherals', [...currentPeripherals, peripheral]);
    }
  };

  const intuneGroups = [
    "Standard User",
    "Developer",
    "Sales",
    "Executive",
    "Marketing",
    "Finance"
  ];

  const softwareOptions = [
    { id: "adobe", label: "Adobe Creative Cloud" },
    { id: "slack", label: "Slack" },
    { id: "zoom", label: "Zoom" },
    { id: "jira", label: "Jira" },
    { id: "tableau", label: "Tableau" },
    { id: "salesforce", label: "Salesforce" }
  ];

  const peripheralOptions = [
    { id: "usb_c", label: "USB-C cables" },
    { id: "dock", label: "Docking station" },
    { id: "mouse_kb", label: "Mouse / Keyboard" },
    { id: "headset", label: "Headset" }
  ];

  // Determine if IT-specific fields should be visible (IT users or admins only)
  const canViewITSpecificFields = currentRole === 'it' || currentRole === 'admin';

  // All users (including HR) can view device and peripheral selections
  const canViewDeviceFields = true;

  return (
    <div className="step-content">
      <div>
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-primary/10 p-2 rounded-full">
            <Laptop className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold">IT Needs</h2>
            <p className="text-muted-foreground">Configure technology resources for the employee</p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6">
          <div className="space-y-2">
            <Label htmlFor="workEmail">Work Email</Label>
            <Input 
              id="workEmail" 
              placeholder="<EMAIL>" 
              value={formData.workEmail || `${formData.firstName.toLowerCase()}.${formData.lastName.toLowerCase()}@company.com`}
              onChange={(e) => updateFormData('workEmail', e.target.value)}
            />
          </div>
          
          {/* Only show M365 license toggle to IT/admin users */}
          {canViewITSpecificFields && (
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="needsM365">Microsoft 365 License</Label>
                <p className="text-sm text-muted-foreground">Assign Microsoft 365 Business license</p>
              </div>
              <Switch 
                id="needsM365" 
                checked={formData.needsM365}
                onCheckedChange={(checked) => updateFormData('needsM365', checked)}
              />
            </div>
          )}
          
          {/* Show device selection to all users */}
          <div className="space-y-3">
            <Label>Computer Type</Label>
            <RadioGroup 
              value={formData.deviceType}
              onValueChange={(value) => updateFormData('deviceType', value)}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="laptop" id="laptop" />
                <Label htmlFor="laptop">Laptop</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="desktop" id="desktop" />
                <Label htmlFor="desktop">Desktop PC</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="none" id="none" />
                <Label htmlFor="none">No computer needed</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-3">
            <Label>Mobile Device Type</Label>
            <RadioGroup 
              value={formData.phoneType}
              onValueChange={(value) => updateFormData('phoneType', value)}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="cellphone" id="cellphone" />
                <Label htmlFor="cellphone">Cell Phone</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="tablet" id="tablet" />
                <Label htmlFor="tablet">Tablet</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="none" id="nomobile" />
                <Label htmlFor="nomobile">No mobile device needed</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-3">
            <Label>Peripherals & Accessories</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {peripheralOptions.map((peripheral) => (
                <div key={peripheral.id} className="flex items-center space-x-2">
                  <Checkbox 
                    id={peripheral.id} 
                    checked={formData.peripherals?.includes(peripheral.id) || false}
                    onCheckedChange={() => handlePeripheralChange(peripheral.id)}
                  />
                  <label
                    htmlFor={peripheral.id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {peripheral.label}
                  </label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="customAccessories">Custom Accessories</Label>
            <Textarea 
              id="customAccessories" 
              placeholder="Additional equipment or special requirements..."
              value={formData.customAccessories}
              onChange={(e) => updateFormData('customAccessories', e.target.value)}
            />
          </div>

          {/* Only show Intune group selection to IT/admin users */}
          {canViewITSpecificFields && (
            <div className="space-y-2">
              <Label htmlFor="intuneGroup">Intune Management Group</Label>
              <Select 
                value={formData.intuneGroup} 
                onValueChange={(value) => updateFormData('intuneGroup', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select management group" />
                </SelectTrigger>
                <SelectContent>
                  {intuneGroups.map(group => (
                    <SelectItem key={group} value={group}>{group}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          <div className="space-y-3">
            <Label>Additional Software</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {softwareOptions.map((software) => (
                <div key={software.id} className="flex items-center space-x-2">
                  <Checkbox 
                    id={software.id} 
                    checked={additionalSoftware.includes(software.id)}
                    onCheckedChange={() => handleSoftwareChange(software.id)}
                  />
                  <label
                    htmlFor={software.id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {software.label}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Shipping Address Section */}
          <div className="space-y-3">
            <Label>Shipping Address</Label>
            <RadioGroup 
              value={shippingAddressType}
              onValueChange={(value) => {
                setShippingAddressType(value);
                if (value === "personal") {
                  updateFormData('shippingAddress', {
                    street: formData.address,
                    city: formData.city,
                    state: formData.state,
                    zip: formData.zip
                  });
                } else {
                  updateFormData('shippingAddress', {
                    street: '',
                    city: '',
                    state: '',
                    zip: ''
                  });
                }
              }}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="personal" id="personal" />
                <Label htmlFor="personal">Use Personal Address</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="custom" />
                <Label htmlFor="custom">Use Different Address</Label>
              </div>
            </RadioGroup>

            {shippingAddressType === "custom" && (
              <div className="space-y-3 mt-3">
                <Input 
                  placeholder="Street Address"
                  value={formData.shippingAddress?.street || ''}
                  onChange={(e) => updateFormData('shippingAddress', {
                    ...formData.shippingAddress,
                    street: e.target.value
                  })}
                />
                <div className="grid grid-cols-3 gap-3">
                  <Input 
                    placeholder="City"
                    value={formData.shippingAddress?.city || ''}
                    onChange={(e) => updateFormData('shippingAddress', {
                      ...formData.shippingAddress,
                      city: e.target.value
                    })}
                  />
                  <Input 
                    placeholder="State"
                    value={formData.shippingAddress?.state || ''}
                    onChange={(e) => updateFormData('shippingAddress', {
                      ...formData.shippingAddress,
                      state: e.target.value
                    })}
                  />
                  <Input 
                    placeholder="ZIP Code"
                    value={formData.shippingAddress?.zip || ''}
                    onChange={(e) => updateFormData('shippingAddress', {
                      ...formData.shippingAddress,
                      zip: e.target.value
                    })}
                  />
                </div>
              </div>
            )}
          </div>

          {canViewITSpecificFields && projectConfig.useEntraID && (
            <div className="mt-4 p-4 bg-blue-50 rounded-md border border-blue-100">
              <h3 className="text-sm font-medium text-blue-800 mb-2">Entra ID / Azure Integration Requirements</h3>
              <ul className="text-sm text-blue-700 space-y-1 list-disc list-inside">
                <li>Azure App Registration (API key/token)</li>
                <li>Permissions for Microsoft Graph API</li>
                <li>Custom attributes via HTTP connector (no Flow connector)</li>
                <li>Passwords must be set by IT, not HR</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ITSetupStep;



