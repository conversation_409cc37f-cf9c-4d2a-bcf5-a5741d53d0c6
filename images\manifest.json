{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "manifestVersion": "1.16", "version": "1.0.0", "id": "e87e0c21-3764-47a9-af10-44ffff1f1ef4", "packageName": "com.hirehub.app", "developer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "websiteUrl": "https://assuring-uniquely-cattle.ngrok-free.app", "privacyUrl": "https://assuring-uniquely-cattle.ngrok-free.app/privacy", "termsOfUseUrl": "https://assuring-uniquely-cattle.ngrok-free.app/terms"}, "icons": {"color": "color.png", "outline": "outline.png"}, "name": {"short": "<PERSON><PERSON><PERSON><PERSON>", "full": "HireHub - Employee Onboarding System"}, "description": {"short": "Streamline your new hire onboarding process", "full": "HireHub helps you streamline your new hire onboarding process with automated HR and IT workflows. Create new employees, track onboarding progress, and ensure a smooth transition for new team members."}, "accentColor": "#0060C0", "staticTabs": [{"entityId": "index", "name": "Home", "contentUrl": "https://assuring-uniquely-cattle.ngrok-free.app/index.html#/tab", "websiteUrl": "https://assuring-uniquely-cattle.ngrok-free.app/index.html#/tab", "scopes": ["personal", "groupChat", "team"]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["assuring-uniquely-cattle.ngrok-free.app"], "webApplicationInfo": {"id": "f2c04aea-776a-4ec1-920e-8aca1807c8a8", "resource": "api://assuring-uniquely-cattle.ngrok-free.app/f2c04aea-776a-4ec1-920e-8aca1807c8a8"}}