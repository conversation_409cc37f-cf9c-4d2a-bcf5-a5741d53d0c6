/**
 * Client-side validation utilities for employee data
 */

import { EmployeeData } from '../services/employeeService';

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * Validate employee data
 * @param employee Employee data to validate
 * @returns ValidationResult Validation result
 */
export function validateEmployee(employee: EmployeeData): ValidationResult {
  const errors: ValidationError[] = [];

  // Required fields
  if (!employee.firstName || employee.firstName.trim() === '') {
    errors.push({ field: 'firstName', message: 'First name is required' });
  } else if (employee.firstName.length > 100) {
    errors.push({ field: 'firstName', message: 'First name cannot exceed 100 characters' });
  }

  if (!employee.lastName || employee.lastName.trim() === '') {
    errors.push({ field: 'lastName', message: 'Last name is required' });
  } else if (employee.lastName.length > 100) {
    errors.push({ field: 'lastName', message: 'Last name cannot exceed 100 characters' });
  }

  if (!employee.email || employee.email.trim() === '') {
    errors.push({ field: 'email', message: 'Email is required' });
  } else if (!isValidEmail(employee.email)) {
    errors.push({ field: 'email', message: 'Email is not valid' });
  } else if (employee.email.length > 100) {
    errors.push({ field: 'email', message: 'Email cannot exceed 100 characters' });
  }

  // Work email validation if provided
  if (employee.workEmail) {
    if (!isValidEmail(employee.workEmail)) {
      errors.push({ field: 'workEmail', message: 'Work email is not valid' });
    } else if (employee.workEmail.length > 100) {
      errors.push({ field: 'workEmail', message: 'Work email cannot exceed 100 characters' });
    }
  }

  // Personal email validation if provided
  if (employee.personalEmail) {
    if (!isValidEmail(employee.personalEmail)) {
      errors.push({ field: 'personalEmail', message: 'Personal email is not valid' });
    } else if (employee.personalEmail.length > 100) {
      errors.push({ field: 'personalEmail', message: 'Personal email cannot exceed 100 characters' });
    }
  }

  // Phone number validation if provided
  if (employee.phoneNumber && !isValidPhoneNumber(employee.phoneNumber)) {
    errors.push({ field: 'phoneNumber', message: 'Phone number is not valid' });
  }

  // Start date validation if provided
  if (employee.startDate) {
    const startDate = new Date(employee.startDate);
    const today = new Date();
    
    if (isNaN(startDate.getTime())) {
      errors.push({ field: 'startDate', message: 'Start date is not valid' });
    } else if (startDate < today) {
      errors.push({ field: 'startDate', message: 'Start date cannot be in the past' });
    }
  }

  // Department validation if provided
  if (employee.department && employee.department.length > 100) {
    errors.push({ field: 'department', message: 'Department cannot exceed 100 characters' });
  }

  // Job title validation if provided
  if (employee.jobTitle && employee.jobTitle.length > 100) {
    errors.push({ field: 'jobTitle', message: 'Job title cannot exceed 100 characters' });
  }

  // Validate hiring manager email if provided
  if (employee.hiringManagerEmail) {
    if (!isValidEmail(employee.hiringManagerEmail)) {
      errors.push({ field: 'hiringManagerEmail', message: 'Hiring manager email is not valid' });
    } else if (employee.hiringManagerEmail.length > 100) {
      errors.push({ field: 'hiringManagerEmail', message: 'Hiring manager email cannot exceed 100 characters' });
    }
  }

  // Address validation if provided
  if (employee.address && employee.address.length > 200) {
    errors.push({ field: 'address', message: 'Address cannot exceed 200 characters' });
  }

  // City validation if provided
  if (employee.city && employee.city.length > 100) {
    errors.push({ field: 'city', message: 'City cannot exceed 100 characters' });
  }

  // State validation if provided
  if (employee.state && employee.state.length > 50) {
    errors.push({ field: 'state', message: 'State cannot exceed 50 characters' });
  }

  // ZIP validation if provided
  if (employee.zip && employee.zip.length > 20) {
    errors.push({ field: 'zip', message: 'ZIP code cannot exceed 20 characters' });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate email address
 * @param email Email address to validate
 * @returns boolean True if the email is valid, false otherwise
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number
 * @param phoneNumber Phone number to validate
 * @returns boolean True if the phone number is valid, false otherwise
 */
function isValidPhoneNumber(phoneNumber: string): boolean {
  // Simple validation for demonstration purposes
  // In a real application, you would use a more sophisticated validation
  const phoneRegex = /^\+?[0-9\s\-()]{10,20}$/;
  return phoneRegex.test(phoneNumber);
}

/**
 * Get field error message
 * @param errors List of validation errors
 * @param fieldName Field name to get error for
 * @returns string Error message or empty string if no error
 */
export function getFieldError(errors: ValidationError[], fieldName: string): string {
  const error = errors.find(e => e.field === fieldName);
  return error ? error.message : '';
}
