import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Settings, Key, Users, Bell, Building2, Upload } from 'lucide-react';
import { AdminConfiguration } from '@/context/types';

const AdminConfigurationPanel: React.FC = () => {
  const [config, setConfig] = useState<AdminConfiguration>({
    graph: {
      tenantId: '',
      clientId: '',
      clientSecret: '',
      scopes: [
        'User.ReadWrite.All',
        'Directory.ReadWrite.All',
        'Group.ReadWrite.All',
        'DeviceManagementConfiguration.ReadWrite.All'
      ],
      redirectUri: window.location.origin,
      useClientCredentials: false
    },
    automation: {
      defaultLicenseSkuId: '',
      defaultSecurityGroup: '',
      defaultIntuneGroup: '',
      defaultTeamsGroup: '',
      passwordPolicy: {
        requireChange: true,
        temporaryPassword: true
      }
    },
    emailDomains: [],
    departmentList: [],
    locationList: [],
    notificationEmails: {
      it: '',
      hr: ''
    },
    branding: {
      logo: ''
    }
  });

  const handleSave = async () => {
    // Save configuration to backend/local storage
    localStorage.setItem('adminConfig', JSON.stringify(config));
    // You would typically save this to your backend as well
  };

  const handleTestConnection = async () => {
    // Implement Microsoft Graph connection test
    try {
      // Test Microsoft Graph connection
      // This would be implemented in your backend
      alert('Connection successful!');
    } catch (error) {
      alert('Connection failed. Please check your credentials.');
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Admin Configuration</h1>
      
      <Tabs defaultValue="graph">
        <TabsList>
          <TabsTrigger value="graph">
            <Key className="h-4 w-4 mr-2" />
            Microsoft Graph
          </TabsTrigger>
          <TabsTrigger value="automation">
            <Settings className="h-4 w-4 mr-2" />
            Automation
          </TabsTrigger>
          <TabsTrigger value="organization">
            <Building2 className="h-4 w-4 mr-2" />
            Organization
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="branding">
            <Upload className="h-4 w-4 mr-2" />
            Branding
          </TabsTrigger>
        </TabsList>

        <TabsContent value="graph">
          <Card>
            <CardHeader>
              <CardTitle>Microsoft Graph Configuration</CardTitle>
              <CardDescription>
                Configure your Microsoft Graph API credentials for user automation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tenantId">Tenant ID</Label>
                <Input
                  id="tenantId"
                  value={config.graph.tenantId}
                  onChange={(e) => setConfig({
                    ...config,
                    graph: { ...config.graph, tenantId: e.target.value }
                  })}
                  placeholder="Enter your Azure AD Tenant ID"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientId">Client ID</Label>
                <Input
                  id="clientId"
                  value={config.graph.clientId}
                  onChange={(e) => setConfig({
                    ...config,
                    graph: { ...config.graph, clientId: e.target.value }
                  })}
                  placeholder="Enter your application Client ID"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="clientSecret">Client Secret</Label>
                <Input
                  id="clientSecret"
                  type="password"
                  value={config.graph.clientSecret}
                  onChange={(e) => setConfig({
                    ...config,
                    graph: { ...config.graph, clientSecret: e.target.value }
                  })}
                  placeholder="Enter your application Client Secret"
                />
              </div>

              <div className="flex items-center space-x-4">
                <Button onClick={handleTestConnection}>Test Connection</Button>
                <Button variant="outline" onClick={handleSave}>Save Configuration</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="automation">
          <Card>
            <CardHeader>
              <CardTitle>Automation Defaults</CardTitle>
              <CardDescription>
                Configure default settings for user automation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="defaultLicense">Default License SKU ID</Label>
                <Input
                  id="defaultLicense"
                  value={config.automation.defaultLicenseSkuId}
                  onChange={(e) => setConfig({
                    ...config,
                    automation: { ...config.automation, defaultLicenseSkuId: e.target.value }
                  })}
                  placeholder="e.g., O365_BUSINESS_PREMIUM"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="defaultSecurityGroup">Default Security Group</Label>
                <Input
                  id="defaultSecurityGroup"
                  value={config.automation.defaultSecurityGroup}
                  onChange={(e) => setConfig({
                    ...config,
                    automation: { ...config.automation, defaultSecurityGroup: e.target.value }
                  })}
                  placeholder="Enter default security group ID"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="requirePasswordChange"
                  checked={config.automation.passwordPolicy.requireChange}
                  onCheckedChange={(checked) => setConfig({
                    ...config,
                    automation: {
                      ...config.automation,
                      passwordPolicy: { ...config.automation.passwordPolicy, requireChange: checked }
                    }
                  })}
                />
                <Label htmlFor="requirePasswordChange">Require password change at first login</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organization">
          <Card>
            <CardHeader>
              <CardTitle>Organization Settings</CardTitle>
              <CardDescription>
                Configure organization-specific settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add organization settings here */}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure email notifications for different departments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="itEmail">IT Department Email</Label>
                <Input
                  id="itEmail"
                  type="email"
                  value={config.notificationEmails.it}
                  onChange={(e) => setConfig({
                    ...config,
                    notificationEmails: { ...config.notificationEmails, it: e.target.value }
                  })}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="hrEmail">HR Department Email</Label>
                <Input
                  id="hrEmail"
                  type="email"
                  value={config.notificationEmails.hr}
                  onChange={(e) => setConfig({
                    ...config,
                    notificationEmails: { ...config.notificationEmails, hr: e.target.value }
                  })}
                  placeholder="<EMAIL>"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branding" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Branding Settings</CardTitle>
              <CardDescription>
                Customize the appearance of your onboarding platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="logo">Company Logo</Label>
                <div className="flex items-center space-x-4">
                  {config.branding?.logo && (
                    <img 
                      src={config.branding.logo} 
                      alt="Current logo" 
                      className="h-12 w-auto"
                    />
                  )}
                  <div className="flex-1">
                    <Input
                      id="logo"
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          // Here you would typically upload the file to your server
                          // and get back a URL. This is just a placeholder using
                          // local URL creation
                          const url = URL.createObjectURL(file);
                          setConfig({
                            ...config,
                            branding: {
                              ...config.branding,
                              logo: url
                            }
                          });
                        }
                      }}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        document.getElementById('logo')?.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Logo
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  Recommended size: 200x50px. PNG or SVG format preferred.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Alert className="mt-8">
        <AlertTitle>Important Note</AlertTitle>
        <AlertDescription>
          Make sure your Azure AD application has the necessary permissions for user management.
          Required permissions include User.ReadWrite.All, Directory.ReadWrite.All, and Group.ReadWrite.All.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default AdminConfigurationPanel;
