/**
 * Utility functions for authentication
 */

import { app } from "@microsoft/teams-js";
import { TeamsUserCredential, TeamsUserCredentialAuthConfig } from "@microsoft/teamsfx";

export const CLIENT_SCOPES: Record<string, string | string[]> = {
  GRAPH: [
    "https://graph.microsoft.com/User.ReadWrite.All",
    "https://graph.microsoft.com/Directory.ReadWrite.All",
    "https://graph.microsoft.com/Group.ReadWrite.All",
    "https://graph.microsoft.com/DeviceManagementConfiguration.ReadWrite.All"
  ],
  DATAVERSE: "https://orgbce2d577.crm.dynamics.com/"
};

let teamsUserCredential: TeamsUserCredential | undefined = undefined;

/**
 * Ensures Teams SDK is initialized
 */
async function ensureTeamsInitialized(): Promise<void> {
  if (!app.isInitialized()) {
    await app.initialize();
  }
}

/**
 * Initialize the Teams user credential
 */
async function initializeTeamsUserCredential(): Promise<TeamsUserCredential> {
  await ensureTeamsInitialized();

  if (!teamsUserCredential) {
    const config: TeamsUserCredentialAuthConfig = {
      clientId: import.meta.env.VITE_CLIENT_ID,
      initiateLoginEndpoint: import.meta.env.VITE_START_LOGIN_PAGE_URL,
    };
    teamsUserCredential = new TeamsUserCredential(config);
  }
  return teamsUserCredential;
}

/**
 * Get token for a specific API (Graph or Dataverse)
 */
export async function getTokenForApi(api: keyof typeof CLIENT_SCOPES): Promise<string> {
  const credential = await initializeTeamsUserCredential();
  try {
    const scopes = CLIENT_SCOPES[api];
    const tokenResponse = await credential.getToken(scopes);
    return tokenResponse?.token || "";
  } catch (error) {
    console.error(`Failed to get token for ${api}:`, error);
    throw error;
  }
}

/**
 * Get auth headers for API calls
 */
export async function getAuthHeaders(api: keyof typeof CLIENT_SCOPES): Promise<HeadersInit> {
  const token = await getTokenForApi(api);
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
}

/**
 * Get user information
 */
export async function getUserInfo(): Promise<any> {
  const credential = await initializeTeamsUserCredential();
  return await credential.getUserInfo();
}

/**
 * Login with specific scopes
 */
export async function login(api: keyof typeof CLIENT_SCOPES): Promise<void> {
  const credential = await initializeTeamsUserCredential();
  await credential.login(CLIENT_SCOPES[api]);
}



