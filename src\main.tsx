import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { OnboardingProvider } from '@/context/OnboardingContext';
import App from './App.tsx';
import './index.css';
import { initializeTeamsSDK } from '@/utils/teamsInit';

// Initialize the Microsoft Teams SDK
initializeTeamsSDK().catch(error => {
  console.error('Failed to initialize Teams SDK:', error);
});

createRoot(document.getElementById("root")!).render(
  <BrowserRouter>
    <OnboardingProvider>
      <App />
    </OnboardingProvider>
  </BrowserRouter>
);

