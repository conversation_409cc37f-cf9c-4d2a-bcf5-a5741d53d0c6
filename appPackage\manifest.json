{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.19/MicrosoftTeams.schema.json", "manifestVersion": "1.19", "version": "1.0.0", "id": "${{TEAMS_APP_ID}}", "developer": {"name": "Teams App, Inc.", "websiteUrl": "${{TAB_ENDPOINT}}", "privacyUrl": "${{TAB_ENDPOINT}}/index.html#/privacy", "termsOfUseUrl": "${{TAB_ENDPOINT}}/index.html#/termsofuse"}, "icons": {"color": "color.png", "outline": "outline.png"}, "name": {"short": "HireHub${{APP_NAME_SUFFIX}}", "full": "Full name for <PERSON><PERSON><PERSON><PERSON>"}, "description": {"short": "Short description of <PERSON><PERSON><PERSON><PERSON>", "full": "Full description of <PERSON><PERSON><PERSON><PERSON>"}, "accentColor": "#FFFFFF", "bots": [], "composeExtensions": [], "configurableTabs": [], "staticTabs": [{"entityId": "index", "name": "Home", "contentUrl": "${{TAB_ENDPOINT}}/index.html#/tab", "websiteUrl": "${{TAB_ENDPOINT}}/index.html#/tab", "scopes": ["personal", "groupChat", "team"]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["${{TAB_HOSTNAME}}"], "webApplicationInfo": {"id": "${{AAD_APP_CLIENT_ID}}", "resource": "api://${{TAB_DOMAIN}}/${{AAD_APP_CLIENT_ID}}"}}