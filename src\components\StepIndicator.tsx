import React from 'react';
import { Check } from 'lucide-react';

interface Step {
  number: number;
  title: string;
}

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  steps: Step[];
  onStepClick: (step: number) => void;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  currentStep,
  totalSteps,
  steps,
  onStepClick
}) => {

  return (
    <div className="mb-8">
      <div className="flex items-center justify-center">
        {steps.map((step, index) => (
          <React.Fragment key={step.number}>
            {/* Step circle */}
            <div
              className={`flex items-center justify-center rounded-full h-10 w-10 border-2
                ${currentStep > step.number
                  ? "bg-primary border-primary text-white"
                  : currentStep === step.number
                    ? "border-primary text-primary"
                    : "border-gray-300 text-gray-400"}
                cursor-pointer transition-all`}
              onClick={() => currentStep > step.number && onStepClick(step.number)}
            >
              {currentStep > step.number ? (
                <Check className="h-5 w-5" />
              ) : (
                <span>{step.number}</span>
              )}
            </div>

            {/* Step title */}
            <div className="hidden md:block mx-2 text-sm font-medium">
              <span className={currentStep >= step.number ? "text-primary" : "text-gray-400"}>
                {step.title}
              </span>
            </div>

            {/* Connector line */}
            {index < steps.length - 1 && (
              <div className={`flex-grow border-t-2 mx-2 ${currentStep > step.number + 1 || (currentStep > step.number && currentStep === step.number + 2) ? "border-primary" : "border-gray-300"}`}></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default StepIndicator;
