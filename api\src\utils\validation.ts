/**
 * Validation utilities for employee data
 */

import { EmployeeRecord } from './dataverseClient';

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * Validate employee data
 * @param employee Employee data to validate
 * @returns ValidationResult Validation result
 */
export function validateEmployee(employee: EmployeeRecord): ValidationResult {
  const errors: ValidationError[] = [];

  // Required fields - matching the HireHub form structure
  if (!employee.firstName || employee.firstName.trim() === '') {
    errors.push({ field: 'firstName', message: 'First name is required' });
  }

  if (!employee.lastName || employee.lastName.trim() === '') {
    errors.push({ field: 'lastName', message: 'Last name is required' });
  }

  if (!employee.email || employee.email.trim() === '') {
    errors.push({ field: 'email', message: 'Email is required' });
  } else if (!isValidEmail(employee.email)) {
    errors.push({ field: 'email', message: 'Email is not valid' });
  }

  // Job information validation
  if (!employee.jobTitle || employee.jobTitle.trim() === '') {
    errors.push({ field: 'jobTitle', message: 'Job title is required' });
  }

  if (!employee.department || employee.department.trim() === '') {
    errors.push({ field: 'department', message: 'Department is required' });
  }

  if (!employee.startDate || employee.startDate.trim() === '') {
    errors.push({ field: 'startDate', message: 'Start date is required' });
  }

  // Optional field validations

  // Work email validation if provided
  if (employee.workEmail && !isValidEmail(employee.workEmail)) {
    errors.push({ field: 'workEmail', message: 'Work email is not valid' });
  }

  // Phone number validation if provided
  if (employee.phoneNumber && !isValidPhoneNumber(employee.phoneNumber)) {
    errors.push({ field: 'phoneNumber', message: 'Phone number is not valid' });
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate email address
 * @param email Email address to validate
 * @returns boolean True if the email is valid, false otherwise
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number
 * @param phoneNumber Phone number to validate
 * @returns boolean True if the phone number is valid, false otherwise
 */
function isValidPhoneNumber(phoneNumber: string): boolean {
  // Simple validation for demonstration purposes
  // In a real application, you would use a more sophisticated validation
  const phoneRegex = /^\+?[0-9\s\-()]{10,20}$/;
  return phoneRegex.test(phoneNumber);
}
