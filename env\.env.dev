# This file includes environment variables that will be committed to git by default.

# Built-in environment variables
TEAMSFX_ENV=dev
APP_NAME_SUFFIX=dev
# Updating AZURE_SUBSCRIPTION_ID or AZURE_RESOURCE_GROUP_NAME after provision may also require an update to RESOURCE_SUFFIX, because some services require a globally unique name across subscriptions/resource groups.
AZURE_SUBSCRIPTION_ID=
AZURE_RESOURCE_GROUP_NAME=
RESOURCE_SUFFIX=

# Generated during provision, you can also add your own variables. If you're adding a secret value, add SECRET_ prefix to the name so Teams Toolkit can handle them properly
TEAMS_APP_ID=
TEAMS_APP_TENANT_ID=
AAD_APP_CLIENT_ID=
AAD_APP_OBJECT_ID=
AAD_APP_ACCESS_AS_USER_PERMISSION_ID=
AAD_APP_TENANT_ID=
AAD_APP_OAUTH_AUTHORITY_HOST=
AAD_APP_OAUTH_AUTHORITY=
TAB_ENDPOINT=
TAB_DOMAIN=
API_FUNCTION_ENDPOINT=
API_FUNCTION_RESOURCE_ID=