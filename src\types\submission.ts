export interface Submission {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  workEmail: string;
  personalEmail: string;
  phoneNumber: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  startDate: string;
  department: string;
  jobTitle: string;
  jobCategory: string;
  branch: string;
  physicalWorkLocation: string;
  physicalWorkAddress: string;
  hiringManagerFirstName: string;
  hiringManagerLastName: string;
  hiringManagerEmail: string;
  isRehire: boolean;
  hireType: string;
  positionType: string;
  replacedFirstName?: string;
  replacedLastName?: string;
  workSchedule: string;
  hrNotes: string;
  deviceType: string;
  phoneType: string;
  needsM365: boolean;
  deskPhoneExt?: string;
  monitorQuantity?: number;
  shipToAddress: string;
  itDevices: string[];
  itNotes: string;
  status: 'pending' | 'approved' | 'rejected' | 'submitted' | 'in_progress' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export interface DashboardProps {
  role: 'IT' | 'HR' | 'manager';
}
