
import React from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UserCircle } from 'lucide-react';

const PersonalInfoStep: React.FC = () => {
  const { formData, updateFormData } = useOnboarding();

  return (
    <div className="step-content">
      <div>
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-primary/10 p-2 rounded-full">
            <UserCircle className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold">Personal & HR Information</h2>
            <p className="text-muted-foreground">Tell us about the new employee</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input 
              id="firstName" 
              placeholder="First Name" 
              value={formData.firstName}
              onChange={(e) => updateFormData('firstName', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input 
              id="lastName" 
              placeholder="Last Name" 
              value={formData.lastName}
              onChange={(e) => updateFormData('lastName', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Personal Email</Label>
            <Input 
              id="email" 
              type="email" 
              placeholder="<EMAIL>" 
              value={formData.email}
              onChange={(e) => updateFormData('email', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="dob">Date of Birth</Label>
            <Input 
              id="dob" 
              type="date" 
              value={formData.dob}
              onChange={(e) => updateFormData('dob', e.target.value)}
            />
          </div>
          
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="address">Address</Label>
            <Input 
              id="address" 
              placeholder="Street Address" 
              value={formData.address}
              onChange={(e) => updateFormData('address', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input 
              id="city" 
              placeholder="City" 
              value={formData.city}
              onChange={(e) => updateFormData('city', e.target.value)}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="state">State</Label>
              <Input 
                id="state" 
                placeholder="State" 
                value={formData.state}
                onChange={(e) => updateFormData('state', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="zip">ZIP Code</Label>
              <Input 
                id="zip" 
                placeholder="ZIP Code" 
                value={formData.zip}
                onChange={(e) => updateFormData('zip', e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonalInfoStep;
