/**
 * Service for interacting with the employee API
 */

import { getTokenForApi } from '../utils/authUtils';
import { validateEmployee, ValidationResult } from '../utils/validation';

/**
 * Interface for employee data
 */
export interface EmployeeData {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  workEmail?: string;
  personalEmail?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  startDate?: string;
  department?: string;
  jobTitle?: string;
  jobCategory?: string;
  branch?: string;
  physicalWorkLocation?: string;
  physicalWorkAddress?: string;
  hiringManagerFirstName?: string;
  hiringManagerLastName?: string;
  hiringManagerEmail?: string;
  isRehire?: boolean;
  hireType?: string;
  positionType?: string;
  replacedFirstName?: string;
  replacedLastName?: string;
  workSchedule?: string;
  hrNotes?: string;
  deviceType?: string;
  phoneType?: string;
  needsM365?: boolean;
  deskPhoneExt?: string;
  itDevices?: string[];
  itNotes?: string;
  submittedBy?: string;
  submittedAt?: string;
  status?: string;
  processedBy?: string;
  processedAt?: string;
}

/**
 * Service for interacting with the employee API
 */
export class EmployeeService {
  private apiBaseUrl: string;

  /**
   * Constructor
   */
  constructor() {
    // Get the API base URL from environment variables
    this.apiBaseUrl = import.meta.env.VITE_FUNC_ENDPOINT || '';
  }

  /**
   * Get the authorization header with the access token
   * @returns Promise<Headers> Headers with the authorization token
   */
  private async getAuthHeaders(): Promise<Headers> {
    try {
      // Try to get token for DATAVERSE first, then fall back to GRAPH if needed
      let token;
      try {
        token = await getTokenForApi('DATAVERSE');
        console.log('Successfully got DATAVERSE token');
      } catch (dataverseError) {
        console.warn('Failed to get DATAVERSE token, trying GRAPH token instead:', dataverseError);
        token = await getTokenForApi('GRAPH');
        console.log('Successfully got GRAPH token as fallback');
      }
      
      const headers = new Headers();
      headers.append('Authorization', `Bearer ${token}`);
      headers.append('Content-Type', 'application/json');
      return headers;
    } catch (error: any) {
      console.error('Error getting auth token:', error);
      // Rethrow with a more descriptive message
      if (error.message && error.message.includes('login first')) {
        throw new Error('Authentication required. Please login first before accessing this feature.');
      }
      throw error;
    }
  }

  /**
   * Validate employee data
   * @param employeeData Employee data to validate
   * @returns ValidationResult Validation result
   */
  validateEmployee(employeeData: EmployeeData): ValidationResult {
    return validateEmployee(employeeData);
  }

  /**
   * Create a new employee
   * @param employeeData Employee data to create
   * @returns Promise<EmployeeData> Created employee data
   * @throws Error if validation fails or API request fails
   */
  async createEmployee(employeeData: EmployeeData): Promise<EmployeeData> {
    try {
      // Validate the employee data client-side first
      const validationResult = this.validateEmployee(employeeData);
      if (!validationResult.valid) {
        throw new Error(`Validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`);
      }

      const headers = await this.getAuthHeaders();
      console.log('Request headers:', headers);
      console.log('Request body:', JSON.stringify(employeeData, null, 2));

      const response = await fetch(`${this.apiBaseUrl}/api/createEmployee`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers,
        body: JSON.stringify(employeeData)
      });

      console.log('Response status:', response.status);
      const responseText = await response.text();
      console.log('Response text:', responseText);

      if (!response.ok) {
        let errorData;
        try {
          errorData = JSON.parse(responseText);
        } catch (e) {
          errorData = { error: responseText };
        }

        // Create a custom error with response data
        const error: any = new Error('Failed to create employee');
        error.response = {
          status: response.status,
          statusText: response.statusText,
          data: errorData
        };
        throw error;
      }

      const data = JSON.parse(responseText);
      return data.employee;
    } catch (error: any) {
      console.error('Detailed error in createEmployee:', {
        message: error?.message,
        stack: error?.stack,
        response: error?.response,
        name: error?.name
      });
      throw error;
    }
  }

  /**
   * Get all employees
   * @returns Promise<EmployeeData[]> List of employees
   */
  async getEmployees(): Promise<EmployeeData[]> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.apiBaseUrl}/api/getEmployees`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`Failed to get employees: ${response.statusText}`);
      }

      const data = await response.json();
      return data.employees;
    } catch (error) {
      console.error('Error getting employees:', error);
      throw error;
    }
  }

  /**
   * Get an employee by ID
   * @param id Employee ID
   * @returns Promise<EmployeeData> Employee data
   */
  async getEmployeeById(id: string): Promise<EmployeeData> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.apiBaseUrl}/api/employee/${id}`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`Failed to get employee: ${response.statusText}`);
      }

      const data = await response.json();
      return data.employee;
    } catch (error) {
      console.error(`Error getting employee with ID ${id}:`, error);
      throw error;
    }
  }
}



