
import React, { createContext, useContext, useState } from 'react';
import {
  OnboardingData,
  ProjectConfig,
  OnboardingContextType,
  UserRole
} from './types';
import {
  defaultFormData,
  defaultProjectConfig,
  sampleSubmissions
} from './defaultValues';
import { DataverseService } from '../services/dataverseService';

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [formData, setFormData] = useState<OnboardingData>(defaultFormData);
  const [projectConfig, setProjectConfig] = useState<ProjectConfig>(defaultProjectConfig);
  const [currentStep, setCurrentStep] = useState(1);
  const [configCompleted, setConfigCompleted] = useState(false);
  const [currentRole, setCurrentRole] = useState<UserRole>('guest');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [submissions, setSubmissions] = useState<OnboardingData[]>(sampleSubmissions);
  const [isLoading, setIsLoading] = useState(false);
  const totalSteps = 5;

  const updateFormData = (field: keyof OnboardingData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateFormDataBatch = (data: Partial<OnboardingData>) => {
    setFormData(prev => ({
      ...prev,
      ...data
    }));
  };

  const updateProjectConfig = (field: keyof ProjectConfig, value: boolean) => {
    setProjectConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateProjectConfigBatch = (config: Partial<ProjectConfig>) => {
    setProjectConfig(prev => ({
      ...prev,
      ...config
    }));
  };

  const addSubmission = (submission: OnboardingData) => {
    setSubmissions(prev => [...prev, submission]);
  };

  const updateSubmission = (index: number, data: Partial<OnboardingData>) => {
    setSubmissions(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], ...data };
      return updated;
    });
  };

  const refreshSubmissions = async (): Promise<void> => {
    // Set a timeout to ensure loading state is reset even if something goes wrong
    const loadingTimeout = setTimeout(() => {
      console.log('Loading timeout reached, resetting loading state');
      setIsLoading(false);
    }, 15000); // 15 seconds max loading time

    try {
      console.log('Starting to refresh submissions...');
      setIsLoading(true);

      // Call the Dataverse service to get the latest employee records
      const response = await DataverseService.getEmployees();
      console.log('Response from DataverseService.getEmployees():', response);

      // If we have a valid response with employees array
      if (response && Array.isArray(response.employees)) {
        console.log(`Found ${response.employees.length} employees in response`);

        // Map the Dataverse employee records to our OnboardingData format
        const dataverseEmployees = response.employees.map((employee: any) => ({
          id: employee.id || `emp-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
          firstName: employee.firstName || '',
          lastName: employee.lastName || '',
          email: employee.email || '',
          dob: employee.dob || '',
          address: employee.address || '',
          city: employee.city || '',
          state: employee.state || '',
          zip: employee.zip || '',
          jobTitle: employee.jobTitle || '',
          department: employee.department || '',
          manager: employee.manager || '',
          startDate: employee.startDate || '',
          workEmail: employee.workEmail || '',
          needsM365: employee.needsM365 || false,
          deviceType: employee.deviceType || '',
          phoneType: employee.phoneType || '',
          intuneGroup: employee.intuneGroup || '',
          additionalSoftware: employee.additionalSoftware || [],
          peripherals: employee.peripherals || [],
          customAccessories: employee.customAccessories || '',
          submittedBy: employee.submittedBy || '',
          submittedAt: employee.submittedAt || null,
          status: employee.status || 'draft',
          processedBy: employee.processedBy || null,
          processedAt: employee.processedAt || null
        }));

        console.log('Mapped employees data:', dataverseEmployees);

        // Update the submissions state with the latest data, even if empty
        setSubmissions(dataverseEmployees);
        console.log('Submissions state updated successfully with', dataverseEmployees.length, 'records');
      } else {
        // Handle case where response doesn't have expected structure
        console.warn('Invalid response format from Dataverse service:', response);
        // Set empty array to show empty state
        setSubmissions([]);
        console.log('Set submissions to empty array due to invalid response format');
      }
    } catch (error) {
      console.error('Error refreshing submissions:', error);
      // Set empty array to show empty state
      setSubmissions([]);
      console.log('Set submissions to empty array due to error');
    } finally {
      // Clear the loading timeout since we're done
      clearTimeout(loadingTimeout);
      setIsLoading(false);
      console.log('Refresh submissions operation completed');
    }
  };

  return (
    <OnboardingContext.Provider value={{
      formData,
      setFormData,
      updateFormData,
      updateFormDataBatch,
      currentStep,
      setCurrentStep,
      totalSteps,
      projectConfig,
      updateProjectConfig,
      updateProjectConfigBatch,
      configCompleted,
      setConfigCompleted,
      currentRole,
      setCurrentRole,
      submissions,
      addSubmission,
      updateSubmission,
      refreshSubmissions,
      isLoading,
      setIsLoading,
      isAuthenticated,
      setIsAuthenticated
    }}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};

// Re-export types for convenience
export * from './types';


