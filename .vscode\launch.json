{"version": "0.2.0", "configurations": [{"name": "Launch Remote in Teams (Edge)", "type": "msedge", "request": "launch", "url": "https://teams.microsoft.com/l/app/${{TEAMS_APP_ID}}?installAppPackage=true&webjoin=true&${account-hint}", "presentation": {"group": "group 1: Teams", "order": 3}, "internalConsoleOptions": "neverOpen"}, {"name": "Launch Remote in Teams (Chrome)", "type": "chrome", "request": "launch", "url": "https://teams.microsoft.com/l/app/${{TEAMS_APP_ID}}?installAppPackage=true&webjoin=true&${account-hint}", "presentation": {"group": "group 1: Teams", "order": 3}, "internalConsoleOptions": "neverOpen"}, {"name": "Launch Remote in Outlook (Edge)", "type": "msedge", "request": "launch", "url": "https://outlook.office.com/host/${{M365_APP_ID}}?${account-hint}", "presentation": {"group": "group 2: Outlook", "order": 3}, "internalConsoleOptions": "neverOpen"}, {"name": "Launch Remote in Outlook (Chrome)", "type": "chrome", "request": "launch", "url": "https://outlook.office.com/host/${{M365_APP_ID}}?${account-hint}", "presentation": {"group": "group 2: Outlook", "order": 3}, "internalConsoleOptions": "neverOpen"}, {"name": "Launch Remote in the Microsoft 365 app (Edge)", "type": "msedge", "request": "launch", "url": "https://www.office.com/m365apps/${{M365_APP_ID}}?auth=2&${account-hint}", "presentation": {"group": "group 3: the Microsoft 365 app", "order": 3}, "internalConsoleOptions": "neverOpen"}, {"name": "Launch Remote in the Microsoft 365 app (Chrome)", "type": "chrome", "request": "launch", "url": "https://www.office.com/m365apps/${{M365_APP_ID}}?auth=2&${account-hint}", "presentation": {"group": "group 3: the Microsoft 365 app", "order": 3}, "internalConsoleOptions": "neverOpen"}, {"name": "Attach to Frontend in Teams (Edge)", "type": "msedge", "request": "launch", "url": "https://teams.microsoft.com/l/app/${{local:TEAMS_APP_ID}}?installAppPackage=true&webjoin=true&${account-hint}", "presentation": {"group": "all", "hidden": true}, "cascadeTerminateToConfigurations": ["Attach to Local Service"], "internalConsoleOptions": "neverOpen"}, {"name": "Attach to Frontend in Teams (Chrome)", "type": "chrome", "request": "launch", "url": "https://teams.microsoft.com/l/app/${{local:TEAMS_APP_ID}}?installAppPackage=true&webjoin=true&${account-hint}", "presentation": {"group": "all", "hidden": true}, "cascadeTerminateToConfigurations": ["Attach to Local Service"], "internalConsoleOptions": "neverOpen"}, {"name": "Attach to Frontend in Outlook (Edge)", "type": "msedge", "request": "launch", "url": "https://outlook.office.com/host/${{local:M365_APP_ID}}?${account-hint}", "presentation": {"group": "all", "hidden": true}, "cascadeTerminateToConfigurations": ["Attach to Local Service"], "internalConsoleOptions": "neverOpen"}, {"name": "Attach to Frontend in Outlook (Chrome)", "type": "chrome", "request": "launch", "url": "https://outlook.office.com/host/${{local:M365_APP_ID}}?${account-hint}", "presentation": {"group": "all", "hidden": true}, "cascadeTerminateToConfigurations": ["Attach to Local Service"], "internalConsoleOptions": "neverOpen"}, {"name": "Attach to Frontend in the Microsoft 365 app (Edge)", "type": "msedge", "request": "launch", "url": "https://www.office.com/m365apps/${{local:M365_APP_ID}}?auth=2&${account-hint}", "presentation": {"group": "all", "hidden": true}, "cascadeTerminateToConfigurations": ["Attach to Local Service"], "internalConsoleOptions": "neverOpen"}, {"name": "Attach to Frontend in the Microsoft 365 app (Chrome)", "type": "chrome", "request": "launch", "url": "https://www.office.com/m365apps/${{local:M365_APP_ID}}?auth=2&${account-hint}", "presentation": {"group": "all", "hidden": true}, "cascadeTerminateToConfigurations": ["Attach to Local Service"], "internalConsoleOptions": "neverOpen"}, {"name": "Attach to Local Service", "type": "node", "request": "attach", "port": 9229, "restart": true, "presentation": {"group": "all", "hidden": true}, "internalConsoleOptions": "neverOpen"}], "compounds": [{"name": "Debug in Teams (Edge)", "configurations": ["Attach to Frontend in Teams (Edge)", "Attach to Local Service"], "preLaunchTask": "Start Teams App Locally", "presentation": {"group": "group 1: Teams", "order": 1}, "stopAll": true}, {"name": "Debug in Teams (Chrome)", "configurations": ["Attach to Frontend in Teams (Chrome)", "Attach to Local Service"], "preLaunchTask": "Start Teams App Locally", "presentation": {"group": "group 1: Teams", "order": 2}, "stopAll": true}, {"name": "Debug in Outlook (Edge)", "configurations": ["Attach to Frontend in Outlook (Edge)", "Attach to Local Service"], "preLaunchTask": "Start Teams App Locally", "presentation": {"group": "group 2: Outlook", "order": 1}, "stopAll": true}, {"name": "Debug in Outlook (Chrome)", "configurations": ["Attach to Frontend in Outlook (Chrome)", "Attach to Local Service"], "preLaunchTask": "Start Teams App Locally", "presentation": {"group": "group 2: Outlook", "order": 2}, "stopAll": true}, {"name": "Debug in the Microsoft 365 app (Edge)", "configurations": ["Attach to Frontend in the Microsoft 365 app (Edge)", "Attach to Local Service"], "preLaunchTask": "Start Teams App Locally", "presentation": {"group": "group 3: the Microsoft 365 app", "order": 1}, "stopAll": true}, {"name": "Debug in the Microsoft 365 app (Chrome)", "configurations": ["Attach to Frontend in the Microsoft 365 app (Chrome)", "Attach to Local Service"], "preLaunchTask": "Start Teams App Locally", "presentation": {"group": "group 3: the Microsoft 365 app", "order": 2}, "stopAll": true}]}