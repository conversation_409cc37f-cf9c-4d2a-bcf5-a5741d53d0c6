import React, { useState, useEffect } from 'react';
import { Shield, Plus, FileText, Search, User, Building, Clock, Laptop, ClipboardList, MonitorSmartphone, RefreshCw, Loader2, FolderOpen } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useOnboarding } from '@/context/OnboardingContext';
import { SubmissionDetails } from '@/components/SubmissionDetails';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from '@/hooks/use-toast';
import { runAzureAutomationForUser } from '@/utils/azureAutomation';
import type { Submission } from '@/types/submission';
import { NewEmployeeForm } from '@/components/NewEmployeeForm';

interface DashboardProps {
  role: 'hr' | 'it' | 'admin';
}

const Dashboard: React.FC<DashboardProps> = ({ role }) => {
  const { setCurrentStep, setFormData, submissions, refreshSubmissions, isLoading, setIsLoading } = useOnboarding();
  const { toast } = useToast();
  const [showSubmissionDetails, setShowSubmissionDetails] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showNewEmployeeForm, setShowNewEmployeeForm] = useState(false);

  // State to track if we've attempted to load data
  const [loadAttempted, setLoadAttempted] = useState(false);

  // Load submissions from Dataverse when the component mounts
  useEffect(() => {
    const loadSubmissions = async () => {
      try {
        console.log('Dashboard: Loading submissions on mount...');
        setLoadAttempted(true);

        // Set a timeout to automatically stop loading after 10 seconds
        const timeoutId = setTimeout(() => {
          if (isLoading) {
            console.log('Dashboard: Loading timeout reached, stopping loading');
            setIsLoading(false);
            toast({
              title: "Warning",
              description: "Loading data took too long. Using available data instead.",
              variant: "default"
            });
          }
        }, 10000);

        await refreshSubmissions();

        // Clear the timeout if loading completes successfully
        clearTimeout(timeoutId);

        console.log('Dashboard: Successfully loaded submissions');
        toast({
          title: "Success",
          description: "Successfully loaded submissions from Dataverse.",
        });
      } catch (error) {
        console.error('Dashboard: Error loading submissions:', error);
        setIsLoading(false);
        toast({
          title: "Error",
          description: "Failed to load submissions from Dataverse. Using sample data instead.",
          variant: "destructive"
        });
      }
    };

    loadSubmissions();
  }, [refreshSubmissions, toast, isLoading, setIsLoading]);

  const isIT = role === 'it';
  const isHR = role === 'hr';

  // Safely filter submissions, handling potential null or undefined values
  const filteredSubmissions = submissions
    .filter(submission => submission && submission.firstName && submission.lastName)
    .filter(submission =>
      `${submission.firstName} ${submission.lastName}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
    );

  const handleNewEmployee = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      department: '',
      startDate: '',
      workEmail: '',
      needsM365: true,
      deviceType: '',
      phoneType: '',
      intuneGroup: '',
      additionalSoftware: [],
      peripherals: [],
      status: 'draft',
      submittedAt: null,
      processedAt: null,
      ...(isHR && {
        dob: '',
        address: '',
        city: '',
        state: '',
        zip: '',
        jobTitle: '',
        manager: '',
        submittedBy: '<EMAIL>',
      })
    });
    setCurrentStep(1);
    setShowNewEmployeeForm(true);
  };

  const handleRunAutomation = async (submission: Submission) => {
    if (isIT) {
      setSelectedSubmission(submission);
      setShowSubmissionDetails(true);
      toast({
        title: "Ready for Review",
        description: "Please review and approve each system configuration.",
      });
    }
  };

  const handleViewDetails = (submission: Submission) => {
    setSelectedSubmission(submission);
    setShowSubmissionDetails(true);
  };

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      submitted: 'bg-amber-50 text-amber-700',
      in_progress: 'bg-blue-50 text-blue-700',
      completed: 'bg-green-50 text-green-700'
    };
    return (
      <Badge className={statusStyles[status as keyof typeof statusStyles]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{isIT ? 'IT' : 'HR'} Dashboard</h1>
          <p className="text-muted-foreground">
            {isIT ? 'Manage employee onboarding automation' : 'Manage employee onboarding submissions'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={async () => {
              try {
                await refreshSubmissions();
                toast({
                  title: "Success",
                  description: "Successfully refreshed submissions from Dataverse.",
                });
              } catch (error) {
                console.error('Error manually refreshing submissions:', error);
                toast({
                  title: "Error",
                  description: "Failed to refresh submissions. Please try again.",
                  variant: "destructive"
                });
              }
            }}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button onClick={handleNewEmployee}>
            <Plus className="mr-2 h-4 w-4" /> New Employee
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{submissions.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {submissions.filter(s => s && s.status === 'pending').length}
            </div>
          </CardContent>
        </Card>

        {/* Add more stat cards as needed */}
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Recent Submissions</CardTitle>
          {isLoading && (
            <div className="flex items-center text-sm text-muted-foreground">
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Loading data...
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="flex items-center py-4">
            <Input
              placeholder="Search submissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {isLoading && submissions.length === 0 ? (
            <div className="py-8 text-center">
              <RefreshCw className="mx-auto h-8 w-8 animate-spin text-muted-foreground mb-4" />
              <p className="text-muted-foreground">Loading submissions from Dataverse...</p>
              <p className="text-muted-foreground mt-2">If this takes too long, there might be connection issues.</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => {
                  // Force stop loading
                  setIsLoading(false);
                }}
              >
                Stop Loading
              </Button>
            </div>
          ) : submissions.length === 0 && loadAttempted ? (
            <div className="py-8 text-center">
              <p className="text-muted-foreground font-medium">No data found from Dataverse.</p>
              <p className="text-muted-foreground mt-2">This could be due to connection issues or no records exist yet.</p>
              <p className="text-muted-foreground mt-2">You can create a new employee or try refreshing again later.</p>
              <div className="flex justify-center mt-4">
                <Button
                  variant="outline"
                  className="mr-2"
                  onClick={async () => {
                    try {
                      await refreshSubmissions();
                    } catch (error) {
                      console.error('Error refreshing:', error);
                    }
                  }}
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Try Again
                </Button>
                <Button
                  className="ml-2"
                  onClick={handleNewEmployee}
                >
                  <Plus className="mr-2 h-4 w-4" /> New Employee
                </Button>
              </div>
            </div>
          ) : submissions.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">No submissions found. Create a new employee to get started.</p>
              <Button
                className="mt-4"
                onClick={handleNewEmployee}
              >
                <Plus className="mr-2 h-4 w-4" /> New Employee
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubmissions.map((submission) => (
                  <TableRow
                    key={submission.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleViewDetails(submission)}
                  >
                    <TableCell>{submission.firstName} {submission.lastName}</TableCell>
                    <TableCell>{submission.department}</TableCell>
                    <TableCell>{submission.startDate}</TableCell>
                    <TableCell>{getStatusBadge(submission.status)}</TableCell>
                    <TableCell className="text-right">
                      {isIT ? (
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewDetails(submission);
                          }}
                        >
                          Review & Approve
                        </Button>
                      ) : (
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewDetails(submission);
                          }}
                        >
                          View Details
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {showSubmissionDetails && selectedSubmission && (
        <SubmissionDetails
          show={showSubmissionDetails}
          onClose={() => setShowSubmissionDetails(false)}
          submission={selectedSubmission}
          isIT={isIT}
        />
      )}

      {showNewEmployeeForm && (
        <NewEmployeeForm
          onClose={() => setShowNewEmployeeForm(false)}
          isHR={isHR}
        />
      )}
    </div>
  );
};

export default Dashboard;












