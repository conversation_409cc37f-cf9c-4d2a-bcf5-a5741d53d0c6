import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  plugins: [react()],
  server: {
    port: 9000,
    strictPort: true,
    headers: {
      'X-Frame-Options': 'ALLOWALL' // 👈 This is crucial for Teams iframe
    },
    proxy: {
      '/api': {
        target: 'http://localhost:7071',
        changeOrigin: true,
        secure: false
      }
    },
    allowedHosts: [
      'localhost',
      'assuring-uniquely-cattle.ngrok-free.app'
    ]
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});

