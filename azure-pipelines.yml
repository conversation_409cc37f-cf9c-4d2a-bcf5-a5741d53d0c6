trigger:
  - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  nodeVersion: '18.x'

stages:
  - stage: Build
    jobs:
      - job: BuildApp
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '$(nodeVersion)'
            displayName: 'Install Node.js'

          - script: |
              npm install
            displayName: 'Install dependencies'
            
          - script: |
              npm run build
            displayName: 'Build app'
            env:
              VITE_CLIENT_ID: $(AAD_APP_CLIENT_ID)
              VITE_START_LOGIN_PAGE_URL: $(TAB_ENDPOINT)/auth-start.html
              VITE_FUNC_NAME: getUserProfile
              VITE_FUNC_ENDPOINT: $(API_FUNCTION_ENDPOINT)
          
          - script: |
              cd api
              npm install
              npm run build
            displayName: 'Build API'
            env:
              M365_CLIENT_ID: $(AAD_APP_CLIENT_ID)
              M365_CLIENT_SECRET: $(AAD_APP_CLIENT_SECRET)
              M365_TENANT_ID: $(AAD_APP_TENANT_ID)
              M365_AUTHORITY_HOST: $(AAD_APP_OAUTH_AUTHORITY_HOST)
          
          # Create deployment artifacts for Azure
          - task: ArchiveFiles@2
            inputs:
              rootFolderOrFile: 'dist'
              includeRootFolder: false
              archiveType: 'zip'
              archiveFile: '$(Build.ArtifactStagingDirectory)/app.zip'
              replaceExistingArchive: true
            displayName: 'Archive app files for Azure deployment'
          
          - task: ArchiveFiles@2
            inputs:
              rootFolderOrFile: 'api'
              includeRootFolder: true
              archiveType: 'zip'
              archiveFile: '$(Build.ArtifactStagingDirectory)/api.zip'
              replaceExistingArchive: true
            displayName: 'Archive API files for Azure deployment'
          
          # Create Teams app package
          - script: |
              npm run build:teams-package
            displayName: 'Build Teams app package'
            env:
              CLIENT_ID: $(AAD_APP_CLIENT_ID)
              CLIENT_SECRET: $(AAD_APP_CLIENT_SECRET)  # Added explicit mapping for the secret
              TAB_DOMAIN: $(TAB_DOMAIN)
          
          - task: CopyFiles@2
            inputs:
              sourceFolder: 'appPackage/build'
              contents: 'appPackage.*.zip'
              targetFolder: '$(Build.ArtifactStagingDirectory)'
            displayName: 'Copy Teams app package'
          
          - task: PublishBuildArtifacts@1
            inputs:
              pathtoPublish: '$(Build.ArtifactStagingDirectory)'
              artifactName: 'drop'
            displayName: 'Publish artifacts'



