import React, { useState } from 'react';
import { EmployeeService } from '../services/employeeService';
import { Button, TextField, Checkbox, Stack, Text } from "@fluentui/react";
import { login } from '../utils/authUtils';
import { useOnboarding } from '../context/OnboardingContext';

export const TestEmployee: React.FC = () => {
  const { refreshSubmissions } = useOnboarding();
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testCreateEmployee = async () => {
    setLoading(true);
    const service = new EmployeeService();

    const employee = {
      firstName: "Test",
      lastName: "User",
      email: "<EMAIL>",
      department: "IT",
      jobTitle: "Developer",
      startDate: "2026-04-30",
      needsM365: true,
      deviceType: "Laptop"
    };

    try {
      const response = await service.createEmployee(employee);

      // Refresh submissions to show the newly created employee
      await refreshSubmissions();

      setResult('Success! Employee created: ' + JSON.stringify(response, null, 2));
    } catch (error: any) {
      // Check if this is an authentication error
      if (error.message && error.message.includes('login first')) {
        setResult('Authentication required. Attempting to log in...');

        try {
          // Trigger login flow with Graph API scopes
          await login('GRAPH');

          // After successful login, retry the operation
          setResult('Login successful. Retrying employee creation...');
          const response = await service.createEmployee(employee);

          // Refresh submissions to show the newly created employee
          await refreshSubmissions();

          setResult('Success! Employee created: ' + JSON.stringify(response, null, 2));
        } catch (loginError: any) {
          // Handle login errors
          const loginErrorDetails = {
            message: loginError.message || 'No error message',
            stack: loginError.stack || 'No stack trace',
            name: loginError.name
          };

          setResult('Login failed: ' + JSON.stringify(loginErrorDetails, null, 2));
          console.error('Login Error:', loginErrorDetails);
        }
      } else {
        // Handle other errors
        const errorDetails = {
          message: error.message || 'No error message',
          stack: error.stack || 'No stack trace',
          response: error.response ? {
            status: error.response.status,
            data: error.response.data
          } : 'No response data',
          name: error.name,
          toString: error.toString()
        };

        setResult('Error creating employee: ' + JSON.stringify(errorDetails, null, 2));
        console.error('Detailed Error:', errorDetails);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async () => {
    setLoading(true);
    setResult('Attempting to login...');

    try {
      await login('GRAPH');
      setResult('Login successful! You can now create an employee.');
    } catch (error: any) {
      const errorDetails = {
        message: error.message || 'No error message',
        stack: error.stack || 'No stack trace',
        name: error.name
      };

      setResult('Login failed: ' + JSON.stringify(errorDetails, null, 2));
      console.error('Login Error:', errorDetails);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Stack tokens={{ childrenGap: 10 }} styles={{ root: { padding: 20 } }}>
      <Stack horizontal tokens={{ childrenGap: 10 }}>
        <Button
          onClick={handleLogin}
          disabled={loading}
        >
          {loading ? 'Logging in...' : 'Login First'}
        </Button>

        <Button
          onClick={testCreateEmployee}
          disabled={loading}
        >
          {loading ? 'Creating...' : 'Create Test Employee'}
        </Button>
      </Stack>

      {result && (
        <Text styles={{
          root: {
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            fontFamily: 'monospace',
            backgroundColor: '#f5f5f5',
            padding: '10px',
            maxHeight: '400px',
            overflowY: 'auto'
          }
        }}>
          {result}
        </Text>
      )}
    </Stack>
  );
};

