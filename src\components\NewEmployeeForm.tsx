import React, { useState } from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import PersonalInfoStep from './steps/PersonalInfoStep';
import ITSetupStep from './steps/ITSetupStep';
import JobInfoStep from './steps/JobInfoStep';
import SummaryStep from './steps/SummaryStep';
import { Button } from '@/components/ui/button';
import { Check, Save, Download } from 'lucide-react';
import { submitToDataverse, validateEmployeeData } from '@/services/dataverseService';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface NewEmployeeFormProps {
  onClose: () => void;
  isHR: boolean;
}

export const NewEmployeeForm: React.FC<NewEmployeeFormProps> = ({ onClose, isHR }) => {
  const { currentStep, setCurrentStep, formData, setFormData, addSubmission, refreshSubmissions } = useOnboarding();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSaveProfile = () => {
    const submission = {
      ...formData,
      submittedAt: new Date().toISOString(),
      status: 'draft',
    };
    addSubmission(submission);
  };

  const handleDownloadPdf = () => {
    // Convert formData to PDF format
    // This is a placeholder - you'll need to implement actual PDF generation
    const data = JSON.stringify(formData, null, 2);
    const blob = new Blob([data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `onboarding-${formData.firstName}-${formData.lastName}.pdf`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  // Initialize with empty data
  React.useEffect(() => {
    setFormData({
      firstName: "",
      lastName: "",
      email: "",
      workEmail: "",
      personalEmail: "",
      phoneNumber: "",
      address: "",
      city: "",
      state: "",
      zip: "",
      startDate: "",
      department: "",
      jobTitle: "",
      jobCategory: "",
      branch: "",
      physicalWorkLocation: "",
      physicalWorkAddress: "",
      hiringManagerFirstName: "",
      hiringManagerLastName: "",
      hiringManagerEmail: "",
      isRehire: false,
      hireType: "newHire",
      positionType: "",
      replacedFirstName: "",
      replacedLastName: "",
      workSchedule: "",
      hrNotes: "",
      deviceType: "",
      phoneType: "",
      needsM365: true,
      deskPhoneExt: "",
      itDevices: [],
      itNotes: ""
    });
  }, []);

  const steps = [
    { number: 1, title: "Personal & HR", component: PersonalInfoStep },
    { number: 2, title: "Job Details", component: JobInfoStep },
    { number: 3, title: "IT Setup", component: ITSetupStep },
    { number: 4, title: "Summary", component: SummaryStep }
  ];

  const renderNavigation = () => {
    if (currentStep === 4) { // Summary step
      return (
        <div className="flex justify-between mt-6">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(currentStep - 1)}
          >
            Previous
          </Button>
          <div className="flex gap-4">
            <Button
              variant="outline"
              onClick={handleSaveProfile}
            >
              <Save className="mr-2 h-4 w-4" />
              Save Profile
            </Button>
            <Button
              variant="outline"
              onClick={handleDownloadPdf}
            >
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
            <Button
              onClick={async () => {
                try {
                  setIsSubmitting(true);
                  setError(null);
                  setSuccess(null);

                  // Validate the form data client-side first
                  const validationResult = validateEmployeeData(formData);
                  if (!validationResult.valid) {
                    const errorMessages = validationResult.errors.map(e => e.message).join(', ');
                    setError(`Please fix the following errors: ${errorMessages}`);
                    return;
                  }

                  // First add to local state
                  const submission = {
                    ...formData,
                    submittedAt: new Date().toISOString(),
                    status: 'submitted',
                  };
                  addSubmission(submission);

                  // Then submit to Dataverse via Azure Function
                  await submitToDataverse(formData);

                  // Refresh submissions from Dataverse to show the latest data
                  await refreshSubmissions();

                  setSuccess('Employee data successfully submitted to Dataverse!');
                  setTimeout(() => {
                    onClose();
                  }, 2000);
                } catch (err) {
                  setError(err instanceof Error ? err.message : 'An error occurred while submitting the form');
                } finally {
                  setIsSubmitting(false);
                }
              }}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'Finish'}
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={() => currentStep > 1 && setCurrentStep(currentStep - 1)}
          disabled={currentStep === 1}
        >
          Previous
        </Button>
        <Button
          onClick={() => setCurrentStep(currentStep + 1)}
        >
          Next
        </Button>
      </div>
    );
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl mx-auto p-6 bg-white">
        {/* Error message */}
        {error && (
          <Alert className="mb-4 bg-red-50 border-red-200">
            <AlertDescription className="text-red-600">{error}</AlertDescription>
          </Alert>
        )}

        {/* Success message */}
        {success && (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <AlertDescription className="text-green-600">{success}</AlertDescription>
          </Alert>
        )}

        {/* Step indicators */}
        <div className="flex items-center justify-center mb-8">
          {steps.map((step, index) => (
            <React.Fragment key={step.number}>
              <div
                className={`flex items-center justify-center rounded-full h-10 w-10 border-2
                  ${currentStep > step.number
                    ? "bg-primary border-primary text-white"
                    : currentStep === step.number
                      ? "border-primary text-primary"
                      : "border-gray-300 text-gray-400"}
                  cursor-pointer transition-all`}
                onClick={() => currentStep > step.number && setCurrentStep(step.number)}
              >
                {currentStep > step.number ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span>{step.number}</span>
                )}
              </div>

              {/* Step title */}
              <div className="hidden md:block mx-2 text-sm font-medium">
                <span className={currentStep >= step.number ? "text-primary" : "text-gray-400"}>
                  {step.title}
                </span>
              </div>

              {/* Connector line */}
              {index < steps.length - 1 && (
                <div className={`flex-grow border-t-2 mx-2 ${
                  currentStep > step.number + 1 || (currentStep > step.number && currentStep === step.number + 2)
                    ? "border-primary"
                    : "border-gray-300"
                }`}></div>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Step content */}
        <div className="flex-1 overflow-y-auto max-h-[400px]">
          {steps.map((step) => (
            currentStep === step.number && <step.component key={step.number} />
          ))}
        </div>

        {/* Navigation */}
        {renderNavigation()}
      </DialogContent>
    </Dialog>
  );
};






















