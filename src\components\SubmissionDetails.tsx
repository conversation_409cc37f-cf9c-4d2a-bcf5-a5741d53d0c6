import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { User, Laptop, ClipboardList, Shield, Check, X, CheckCircle2, Clock, Loader2, XCircle, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

interface SubmissionDetailsProps {
  show: boolean;
  onClose: () => void;
  submission: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    workEmail: string;
    personalEmail: string;
    phoneNumber: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    startDate: string;
    department: string;
    jobTitle: string;
    jobCategory: string;
    branch: string;
    physicalWorkLocation: string;
    physicalWorkAddress: string;
    hiringManagerFirstName: string;
    hiringManagerLastName: string;
    hiringManagerEmail: string;
    isRehire: boolean;
    hireType: string;
    positionType: string;
    replacedFirstName?: string;
    replacedLastName?: string;
    workSchedule: string;
    hrNotes: string;
    deviceType: string;
    phoneType: string;
    needsM365: boolean;
    deskPhoneExt?: string;
    monitorQuantity?: number;
    shipToAddress: string;
    itDevices: string[];
    itNotes: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  };
  isIT: boolean;
}

interface AutomationTask {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected' | 'in_progress' | 'completed';
  dependsOn?: string[];
  completedAt?: string;
  error?: string;
}

export const SubmissionDetails: React.FC<SubmissionDetailsProps> = ({
  show,
  onClose,
  submission,
  isIT
}) => {
  const [automationTasks, setAutomationTasks] = useState<AutomationTask[]>([
    {
      id: 'azure-account',
      name: 'Create Azure AD Account',
      description: `Create account for ${submission.firstName} ${submission.lastName}`,
      status: 'pending'
    },
    {
      id: 'email',
      name: 'Configure Email Account',
      description: `Setup ${submission.workEmail || 'work email'}`,
      status: 'pending',
      dependsOn: ['azure-account']
    },
    {
      id: 'm365',
      name: 'Assign M365 License',
      description: 'Assign Business Premium license',
      status: 'pending',
      dependsOn: ['azure-account', 'email']
    },
    {
      id: 'intune',
      name: 'Configure Intune Enrollment',
      description: `Setup ${submission.deviceType} device management`,
      status: 'pending',
      dependsOn: ['azure-account']
    }
  ].filter(task => {
    if (task.id === 'm365' && !submission.needsM365) return false;
    if (task.id === 'intune' && submission.deviceType === 'none') return false;
    return true;
  }));

  const handleBulkApprove = () => {
    setAutomationTasks(tasks => 
      tasks.map(task => ({ ...task, status: 'approved' }))
    );
  };

  const handleApproveTask = async (taskId: string) => {
    setAutomationTasks(tasks => 
      tasks.map(task => {
        if (task.id === taskId) {
          return { ...task, status: 'in_progress' };
        }
        return task;
      })
    );

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAutomationTasks(tasks => 
        tasks.map(task => {
          if (task.id === taskId) {
            return { 
              ...task, 
              status: 'completed',
              completedAt: new Date().toISOString()
            };
          }
          return task;
        })
      );
    } catch (error) {
      setAutomationTasks(tasks => 
        tasks.map(task => {
          if (task.id === taskId) {
            return { 
              ...task, 
              status: 'rejected',
              error: 'Failed to complete automation task'
            };
          }
          return task;
        })
      );
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'in_progress': return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'completed': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'rejected': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return null;
    }
  };

  const isTaskEnabled = (task: AutomationTask) => {
    if (!task.dependsOn) return true;
    return task.dependsOn.every(dependencyId => 
      automationTasks.find(t => t.id === dependencyId)?.status === 'approved'
    );
  };

  return (
    <Dialog open={show} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <DialogTitle className="text-2xl flex items-center gap-2">
            <ClipboardList className="h-6 w-6" />
            New Hire Submission Details
          </DialogTitle>
          <p className="text-muted-foreground">
            Viewing submission for {submission.firstName} {submission.lastName} - {submission.jobTitle}
          </p>
        </DialogHeader>

        <div className="h-[calc(90vh-100px)]">
          <Tabs defaultValue="employee" className="h-full flex flex-col">
            <TabsList className="px-6 pt-2">
              <TabsTrigger value="employee">
                <User className="h-4 w-4 mr-2" />
                Employee Information
              </TabsTrigger>
              <TabsTrigger value="job">
                <ClipboardList className="h-4 w-4 mr-2" />
                Job Details
              </TabsTrigger>
              <TabsTrigger value="it">
                <Laptop className="h-4 w-4 mr-2" />
                IT Equipment & Access
              </TabsTrigger>
              {isIT && (
                <TabsTrigger value="approve">
                  <Shield className="h-4 w-4 mr-2" />
                  Review & Approve
                </TabsTrigger>
              )}
            </TabsList>

            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                <TabsContent value="employee">
                  <div className="grid grid-cols-2 gap-6">
                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="font-medium mb-4">Personal Information</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm text-muted-foreground">Full Name</h4>
                            <p className="font-medium">{submission.firstName} {submission.lastName}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Personal Email</h4>
                            <p className="font-medium">{submission.personalEmail}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Phone Number</h4>
                            <p className="font-medium">{submission.phoneNumber}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Home Address</h4>
                            <p className="font-medium">
                              {submission.address}<br />
                              {submission.city}, {submission.state} {submission.zip}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="font-medium mb-4">Work Information</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm text-muted-foreground">Work Email</h4>
                            <p className="font-medium">{submission.workEmail || 'Not yet assigned'}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Start Date</h4>
                            <p className="font-medium">{submission.startDate}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Department</h4>
                            <p className="font-medium">{submission.department}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="job">
                  <div className="grid grid-cols-2 gap-6">
                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="font-medium mb-4">Position Details</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm text-muted-foreground">Job Title</h4>
                            <p className="font-medium">{submission.jobTitle}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Job Category</h4>
                            <p className="font-medium">{submission.jobCategory}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Branch</h4>
                            <p className="font-medium">{submission.branch}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Work Location</h4>
                            <p className="font-medium">{submission.physicalWorkAddress}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Work Schedule</h4>
                            <p className="font-medium">{submission.workSchedule}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="font-medium mb-4">Hiring Details</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm text-muted-foreground">Hiring Manager</h4>
                            <p className="font-medium">
                              {submission.hiringManagerFirstName} {submission.hiringManagerLastName}
                            </p>
                            <p className="text-sm text-muted-foreground">{submission.hiringManagerEmail}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Position Type</h4>
                            <p className="font-medium">{submission.positionType}</p>
                          </div>
                          {submission.positionType === 'replacement' && (
                            <div>
                              <h4 className="text-sm text-muted-foreground">Replacing Employee</h4>
                              <p className="font-medium">
                                {submission.replacedFirstName} {submission.replacedLastName}
                              </p>
                            </div>
                          )}
                          <div>
                            <h4 className="text-sm text-muted-foreground">Hire Type</h4>
                            <p className="font-medium">
                              {submission.isRehire ? 'Rehire' : submission.hireType}
                            </p>
                          </div>
                          {submission.hrNotes && (
                            <div>
                              <h4 className="text-sm text-muted-foreground">HR Notes</h4>
                              <p className="font-medium">{submission.hrNotes}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="it">
                  <div className="grid grid-cols-2 gap-6">
                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="font-medium mb-4">Equipment</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm text-muted-foreground">Computer</h4>
                            <p className="font-medium capitalize">{submission.deviceType}</p>
                          </div>
                          <div>
                            <h4 className="text-sm text-muted-foreground">Phone</h4>
                            <p className="font-medium capitalize">{submission.phoneType}</p>
                          </div>
                          {submission.deskPhoneExt && (
                            <div>
                              <h4 className="text-sm text-muted-foreground">Desk Phone Extension</h4>
                              <p className="font-medium">{submission.deskPhoneExt}</p>
                            </div>
                          )}
                          {submission.monitorQuantity && (
                            <div>
                              <h4 className="text-sm text-muted-foreground">Additional Monitors</h4>
                              <p className="font-medium">{submission.monitorQuantity}</p>
                            </div>
                          )}
                          <div>
                            <h4 className="text-sm text-muted-foreground">Ship To Address</h4>
                            <p className="font-medium">{submission.shipToAddress}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="pt-6">
                        <h3 className="font-medium mb-4">Access & Software</h3>
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm text-muted-foreground">Microsoft 365</h4>
                            <Badge variant={submission.needsM365 ? "default" : "secondary"}>
                              {submission.needsM365 ? 'Required' : 'Not Required'}
                            </Badge>
                          </div>
                          {submission.itDevices && submission.itDevices.length > 0 && (
                            <div>
                              <h4 className="text-sm text-muted-foreground">Additional Equipment</h4>
                              <ul className="list-disc list-inside">
                                {submission.itDevices.map((device, index) => (
                                  <li key={index} className="font-medium">{device}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                          {submission.itNotes && (
                            <div>
                              <h4 className="text-sm text-muted-foreground">IT Notes</h4>
                              <p className="font-medium">{submission.itNotes}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="approve">
                  <div className="grid grid-cols-1 gap-6">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="flex justify-between items-center mb-6">
                          <div>
                            <h3 className="font-medium">System Access & Automation Tasks</h3>
                            <p className="text-sm text-muted-foreground">
                              Review and approve tasks to trigger automated system configuration
                            </p>
                          </div>
                          <Button 
                            onClick={handleBulkApprove}
                            disabled={automationTasks.some(task => task.status !== 'pending')}
                          >
                            <Check className="h-4 w-4 mr-2" />
                            Approve All
                          </Button>
                        </div>
                        <div className="space-y-4">
                          {automationTasks.map((task) => (
                            <Card key={task.id} className="border border-border">
                              <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                  <div className="space-y-1">
                                    <div className="flex items-center gap-2">
                                      {getStatusIcon(task.status)}
                                      <h4 className="font-medium">{task.name}</h4>
                                    </div>
                                    <p className="text-sm text-muted-foreground">{task.description}</p>
                                    {task.error && (
                                      <p className="text-sm text-red-600">{task.error}</p>
                                    )}
                                    {task.completedAt && (
                                      <p className="text-sm text-muted-foreground">
                                        Completed at: {new Date(task.completedAt).toLocaleString()}
                                      </p>
                                    )}
                                  </div>
                                  
                                  {task.status === 'pending' && (
                                    <Button
                                      size="sm"
                                      onClick={() => handleApproveTask(task.id)}
                                      disabled={!isTaskEnabled(task)}
                                    >
                                      <Play className="h-4 w-4 mr-2" />
                                      Run Task
                                    </Button>
                                  )}
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </ScrollArea>
            </div>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};





