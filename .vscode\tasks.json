// This file is automatically generated by Teams Toolkit.
// The teamsfx tasks defined in this file require Teams Toolkit version >= 5.0.0.
// See https://aka.ms/teamsfx-tasks for details on how to customize each task.
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Start Teams App Locally",
            "dependsOn": [
                "Validate prerequisites",
                "Provision",
                "Deploy",
                "Start application"
            ],
            "dependsOrder": "sequence"
        },
        {
            // Check if all required prerequisites.
            // See https://aka.ms/teamsfx-tasks/check-prerequisites to know the details and how to customize the args.
            "label": "Validate prerequisites",
            "type": "teamsfx",
            "command": "debug-check-prerequisites",
            "args": {
                "prerequisites": [
                    "nodejs", // Validate if Node.js is installed.
                    "m365Account", // Sign-in prompt for Microsoft 365 account, then validate if the account enables the sideloading permission.
                    "portOccupancy" // Validate available ports to ensure those debug ones are not occupied.
                ],
                "portOccupancy": [
                    53000, // tab service port
                    7071, // backend service port
                    9229 // backend inspector port for Node.js debugger
                ]
            }
        },
        {
            // Create the debug resources.
            // See https://aka.ms/teamsfx-tasks/provision to know the details and how to customize the args.
            "label": "Provision",
            "type": "teamsfx",
            "command": "provision",
            "args": {
                "env": "local"
            }
        },
        {
            // Build project.
            // See See https://aka.ms/teamsfx-tasks/deploy to know the details and how to customize the args.
            "label": "Deploy",
            "type": "teamsfx",
            "command": "deploy",
            "args": {
                "env": "local"
            }
        },
        {
            "label": "Start application",
            "dependsOn": [
                "Start frontend",
                "Start backend"
            ]
        },
        {
            "label": "Start frontend",
            "type": "shell",
            "command": "npm run dev-tab:teamsfx",
            "isBackground": true,
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "problemMatcher": {
                "pattern": {
                    "regexp": "^.*$",
                    "file": 0,
                    "location": 1,
                    "message": 2
                },
                "background": {
                    "activeOnStart": true,
                    "beginsPattern": ".*",
                    "endsPattern": "Compiled|Failed|compiled|failed|ready"
                }
            }
        },
        {
            "label": "Start backend",
            "type": "shell",
            "command": "npm run dev:teamsfx",
            "isBackground": true,
            "options": {
                "cwd": "${workspaceFolder}/api",
                "env": {
                    "PATH": "${workspaceFolder}/devTools/func:${env:PATH}"
                }
            },
            "windows": {
                "options": {
                    "env": {
                        "PATH": "${workspaceFolder}/devTools/func;${env:PATH}"
                    }
                }
            },
            "problemMatcher": {
                "pattern": {
                    "regexp": "^.*$",
                    "file": 0,
                    "location": 1,
                    "message": 2
                },
                "background": {
                    "activeOnStart": true,
                    "beginsPattern": "^.*(Job host stopped|signaling restart).*$",
                    "endsPattern": "^.*(Worker process started and initialized|Host lock lease acquired by instance ID).*$"
                }
            },
            "presentation": {
                "reveal": "silent"
            },
            "dependsOn": "Watch backend"
        },
        {
            "label": "Watch backend",
            "type": "shell",
            "command": "npm run watch:teamsfx",
            "isBackground": true,
            "options": {
                "cwd": "${workspaceFolder}/api"
            },
            "problemMatcher": "$tsc-watch",
            "presentation": {
                "reveal": "silent"
            }
        }
    ]
}
