
import React from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Button } from '@/components/ui/button';
import { CheckCircle2 } from 'lucide-react';

const DemoCompletedStep: React.FC = () => {
  const { formData, currentRole } = useOnboarding();

  return (
    <div className="step-content">
      <div className="text-center mb-10">
        <div className="mx-auto w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-4">
          <CheckCircle2 className="h-8 w-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold mb-2">Request Submitted</h2>
        <p className="text-muted-foreground text-lg">
          {currentRole === 'hr' 
            ? "Your onboarding request has been sent to the IT department for processing."
            : "The onboarding process has been initiated for the new employee."}
        </p>
      </div>

      <div className="mb-8">
        <div className="bg-muted/30 rounded-lg p-5">
          <h3 className="text-xl font-medium mb-3">Employee Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-sm">
            <div>
              <p className="text-muted-foreground">Full Name</p>
              <p className="font-medium">{formData.firstName} {formData.lastName}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Work Email</p>
              <p className="font-medium">{formData.workEmail}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Job Title</p>
              <p className="font-medium">{formData.jobTitle}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Department</p>
              <p className="font-medium">{formData.department}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Start Date</p>
              <p className="font-medium">{formData.startDate}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Manager</p>
              <p className="font-medium">{formData.manager}</p>
            </div>
          </div>
        </div>
      </div>
      
      {currentRole === 'hr' && (
        <div className="bg-blue-50 rounded-lg p-6 text-center">
          <h3 className="text-xl font-medium mb-2">What happens next?</h3>
          <p className="text-muted-foreground mb-0">
            The IT department will process this request and set up the necessary resources 
            for the new employee. You'll receive a notification when the process is complete.
          </p>
        </div>
      )}
      
      {currentRole === 'it' && (
        <div className="bg-blue-50 rounded-lg p-6 text-center">
          <h3 className="text-xl font-medium mb-2">Onboarding Complete</h3>
          <p className="text-muted-foreground mb-0">
            All resources have been provisioned for this employee according to company policies.
          </p>
        </div>
      )}
    </div>
  );
};

export default DemoCompletedStep;
