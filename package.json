{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "engines": {"node": "18 || 20 || 22"}, "type": "module", "private": true, "dependencies": {"@azure/msal-browser": "^4.11.0", "@fluentui/react-components": "^9.55.1", "@hookform/resolvers": "^5.0.1", "@microsoft/teams-js": "^2.31.1", "@microsoft/teamsfx": "^3.0.0", "@microsoft/teamsfx-react": "^4.0.0", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "axios": "^0.21.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.488.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-router-dom": "^6.8.0", "tailwind-merge": "^3.2.0", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.4", "@types/node": "^18.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-react": "^4.3.1", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.14", "concurrently": "^8.2.2", "env-cmd": "^10.1.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^4.1.2", "vite": "^5.4.2"}, "scripts": {"dev:teamsfx": "concurrently \"npm run dev-tab:teamsfx\" \"npm run dev-api:teamsfx\"", "dev-tab:teamsfx": "env-cmd --silent -f .localConfigs npm run start", "dev-api:teamsfx": "cd api && npm run dev:teamsfx", "dev": "vite", "start": "vite", "build": "tsc && vite build", "test": "echo \"Error: no test specified\" && exit 1", "serve": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "."}