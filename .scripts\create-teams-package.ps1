# Create Teams app package
Write-Host "Creating Teams app package..." -ForegroundColor Cyan

# Check if the app ID is provided
$appId = Read-Host "Enter your Azure AD app ID (leave blank to use the one from .env.local)"

# If no app ID provided, try to get it from .env.local
if ([string]::IsNullOrWhiteSpace($appId)) {
    $envPath = "..\\.env.local"
    if (Test-Path $envPath) {
        $envContent = Get-Content $envPath
        foreach ($line in $envContent) {
            if ($line -match "VITE_CLIENT_ID=(.+)") {
                $appId = $matches[1]
                Write-Host "Found app ID in .env.local: $appId" -ForegroundColor Green
                break
            }
        }
    }
}

# If still no app ID, prompt again
if ([string]::IsNullOrWhiteSpace($appId)) {
    $appId = Read-Host "Could not find app ID in .env.local. Please enter your Azure AD app ID"
    if ([string]::IsNullOrWhiteSpace($appId)) {
        Write-Host "No app ID provided. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Set ngrok domain
$ngrokDomain = "assuring-uniquely-cattle.ngrok-free.app"

# Remove any protocol if present
$hostname = $ngrokDomain -replace "https://", "" -replace "http://", ""

Write-Host "Using ngrok domain: $hostname" -ForegroundColor Green

# Create the manifest directory if it doesn't exist
$manifestDir = "..\\manifest"
if (-not (Test-Path $manifestDir)) {
    New-Item -ItemType Directory -Path $manifestDir | Out-Null
}

# Copy the icon files to the manifest directory
$imagesDir = "..\\images"
Copy-Item "$imagesDir\\color.png" "$manifestDir\\color.png" -Force
Copy-Item "$imagesDir\\outline.png" "$manifestDir\\outline.png" -Force

# Create the manifest.json file
$manifestContent = @"
{
  "$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json",
  "manifestVersion": "1.16",
  "version": "1.0.0",
  "id": "e87e0c21-3764-47a9-af10-44ffff1f1ef4",
  "packageName": "com.hirehub.app",
  "developer": {
    "name": "HireHub",
    "websiteUrl": "https://hirehub.example.com",
    "privacyUrl": "https://hirehub.example.com/privacy",
    "termsOfUseUrl": "https://hirehub.example.com/terms"
  },
  "icons": {
    "color": "color.png",
    "outline": "outline.png"
  },
  "name": {
    "short": "HireHub",
    "full": "HireHub - Employee Onboarding System"
  },
  "description": {
    "short": "Streamline your new hire onboarding process",
    "full": "HireHub helps you streamline your new hire onboarding process with automated HR and IT workflows. Create new employees, track onboarding progress, and ensure a smooth transition for new team members."
  },
  "accentColor": "#0060C0",
  "staticTabs": [
    {
      "entityId": "dashboard",
      "name": "Dashboard",
      "contentUrl": "https://$hostname/dashboard",
      "websiteUrl": "https://$hostname/dashboard",
      "scopes": [
        "personal"
      ]
    },
    {
      "entityId": "onboarding",
      "name": "New Employee",
      "contentUrl": "https://$hostname/onboarding",
      "websiteUrl": "https://$hostname/onboarding",
      "scopes": [
        "personal"
      ]
    }
  ],
  "permissions": [
    "identity",
    "messageTeamMembers"
  ],
  "validDomains": [
    "$hostname",
    "localhost",
    "*.hirehub.example.com"
  ],
  "webApplicationInfo": {
    "id": "$appId",
    "resource": "api://$hostname/$appId"
  },
  "isFullScreen": true
}
"@

$manifestPath = "$manifestDir\\manifest.json"
$manifestContent | Out-File -FilePath $manifestPath -Encoding utf8

Write-Host "Created manifest.json with app ID: $appId and hostname: $hostname" -ForegroundColor Green

# Create the zip file
try {
    $zipPath = "$manifestDir\\HireHub.zip"
    Compress-Archive -Path "$manifestDir\\manifest.json", "$manifestDir\\color.png", "$manifestDir\\outline.png" -DestinationPath $zipPath -Force
    Write-Host "Teams app package created successfully: $zipPath" -ForegroundColor Green

    # Open the folder containing the zip file
    explorer.exe (Split-Path -Parent $zipPath)
} catch {
    Write-Host "Error creating Teams app package: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`nTo install the app in Teams:" -ForegroundColor Yellow
Write-Host "1. Go to Microsoft Teams" -ForegroundColor Yellow
Write-Host "2. Click on Apps in the left sidebar" -ForegroundColor Yellow
Write-Host "3. Click 'Manage your apps' at the bottom" -ForegroundColor Yellow
Write-Host "4. Click 'Upload an app' > 'Upload a custom app'" -ForegroundColor Yellow
Write-Host "5. Select the HireHub.zip file" -ForegroundColor Yellow
