
// Define user roles
export type UserRole = 'hr' | 'it' | 'admin' | 'guest';

// Define submission status
export type SubmissionStatus = 'draft' | 'submitted' | 'in_progress' | 'completed';

// Define the type for our form data
export interface ShippingAddress {
  street: string;
  city: string;
  state: string;
  zip: string;
}

export interface OnboardingData {
  // Personal & HR Info
  firstName: string;
  lastName: string;
  email: string;
  dob: string;
  address: string;
  city: string;
  state: string;
  zip: string;

  // Job Info
  jobTitle: string;
  department: string;
  manager: string;
  startDate: string;

  // IT Needs
  workEmail: string;
  needsM365: boolean;
  deviceType: string;
  phoneType: string;
  intuneGroup: string;
  additionalSoftware: string[];
  peripherals: string[];
  customAccessories: string;

  // Submission metadata
  submittedBy: string;
  submittedAt: string | null;
  status: SubmissionStatus;
  processedBy: string | null;
  processedAt: string | null;

  // Add shipping address
  shippingAddress?: ShippingAddress;
}

// Define the type for project configuration
export interface ProjectConfig {
  useEntraID: boolean;
  useM365: boolean;
  useIntune: boolean;
  useTeams: boolean;
  useSlack: boolean;
  useHRSystem: boolean;
  useDynamicGroups: boolean;
  useCustomFields: boolean;
}

// Add new types for admin configuration
export interface GraphConfiguration {
  tenantId: string;
  clientId: string;
  clientSecret?: string;
  scopes: string[];
  redirectUri: string;
  useClientCredentials: boolean;
}

export interface AutomationConfiguration {
  defaultLicenseSkuId: string;
  defaultSecurityGroup: string;
  defaultIntuneGroup: string;
  defaultTeamsGroup: string;
  passwordPolicy: {
    requireChange: boolean;
    temporaryPassword: boolean;
  };
}

export interface AdminConfiguration {
  graph: GraphConfiguration;
  automation: AutomationConfiguration;
  emailDomains: string[];
  departmentList: string[];
  locationList: string[];
  notificationEmails: {
    it: string;
    hr: string;
  };
}

export interface OnboardingContextType {
  formData: OnboardingData;
  setFormData: React.Dispatch<React.SetStateAction<OnboardingData>>;
  updateFormData: (field: keyof OnboardingData, value: any) => void;
  updateFormDataBatch: (data: Partial<OnboardingData>) => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  totalSteps: number;
  projectConfig: ProjectConfig;
  updateProjectConfig: (field: keyof ProjectConfig, value: boolean) => void;
  updateProjectConfigBatch: (config: Partial<ProjectConfig>) => void;
  configCompleted: boolean;
  setConfigCompleted: (completed: boolean) => void;
  currentRole: UserRole;
  setCurrentRole: (role: UserRole) => void;
  submissions: OnboardingData[];
  addSubmission: (submission: OnboardingData) => void;
  updateSubmission: (index: number, data: Partial<OnboardingData>) => void;
  refreshSubmissions: () => Promise<void>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  isAuthenticated: boolean;
  setIsAuthenticated: (authenticated: boolean) => void;
}


