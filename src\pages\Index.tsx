
import React from 'react';
import { OnboardingProvider, useOnboarding } from '@/context/OnboardingContext';
import { useNavigate, useLocation } from 'react-router-dom';
import Hero from '@/components/Hero';
import Banner from '@/components/Banner';
import OnboardingForm from '@/components/OnboardingForm';
import RoleSelector from '@/components/RoleSelector';
import Dashboard from '@/components/Dashboard';

const OnboardingContent: React.FC = () => {
  const { currentRole, isAuthenticated } = useOnboarding();
  
  if (!isAuthenticated) {
    return <RoleSelector />;
  }
  
  if (currentRole === 'hr' || currentRole === 'it' || currentRole === 'admin') {
    return <Dashboard role={currentRole} />;
  }
  
  return <OnboardingForm />;
};

const Index: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleGetStarted = () => {
    navigate('/onboarding', { replace: false });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-blue-50/50">
      <Banner />
      <div className="container max-w-6xl mx-auto pt-6">
        {location.pathname === '/' ? (
          <Hero onGetStarted={handleGetStarted} />
        ) : (
          <OnboardingProvider>
            <div className="animate-fade-in py-6">
              <OnboardingContent />
            </div>
          </OnboardingProvider>
        )}
      </div>
    </div>
  );
};

export default Index;







