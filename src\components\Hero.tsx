
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowR<PERSON>, Users, Server, FileCheck } from 'lucide-react';

const Hero: React.FC<{ onGetStarted: () => void }> = ({ onGetStarted }) => {
  return (
    <div className="py-12 md:py-20 px-6 text-center">
      <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-blue-700 bg-clip-text text-transparent">
        Streamline New Hire Onboarding
      </h1>
      <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
        Transform your onboarding process from days to minutes. Automate HR and IT setup for new employees with one unified workflow.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-12">
        <div className="bg-white p-6 rounded-xl shadow-md">
          <div className="bg-primary/10 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-medium mb-2">HR Automation</h3>
          <p className="text-muted-foreground">Streamline employee records, documentation, and compliance paperwork.</p>
        </div>
        
        <div className="bg-white p-6 rounded-xl shadow-md">
          <div className="bg-primary/10 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
            <Server className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-medium mb-2">IT Integration</h3>
          <p className="text-muted-foreground">Automatically set up accounts, devices, and access permissions.</p>
        </div>
        
        <div className="bg-white p-6 rounded-xl shadow-md">
          <div className="bg-primary/10 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
            <FileCheck className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-medium mb-2">Unified Process</h3>
          <p className="text-muted-foreground">Combine HR and IT workflows into one seamless onboarding experience.</p>
        </div>
      </div>
      
      <Button onClick={onGetStarted} size="lg" className="group">
        Get Started
        <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
      </Button>
    </div>
  );
};

export default Hero;
