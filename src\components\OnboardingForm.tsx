import React from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, ArrowRight, Check, UserCircle, Briefcase, Laptop, FileCheck } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import StepIndicator from './StepIndicator';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

const OnboardingForm: React.FC = () => {
  const {
    formData,
    currentStep,
    setCurrentStep,
    totalSteps,
    updateFormData,
    addSubmission
  } = useOnboarding();

  const { toast } = useToast();

  const steps = [
    { number: 1, title: "Personal & HR" },
    { number: 2, title: "Job Details" },
    { number: 3, title: "IT Setup" },
    { number: 4, title: "Summary" }
  ];

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    // In a real app, this would submit the form data to a backend
    toast({
      title: "Onboarding Form Submitted",
      description: `New hire details for ${formData.firstName} ${formData.lastName} have been submitted successfully.`,
    });
  };

  return (
    <div className="max-w-3xl mx-auto px-4 pb-16 animate-fade-in">
      <StepIndicator
        currentStep={currentStep}
        totalSteps={totalSteps}
        steps={steps}
        onStepClick={setCurrentStep}
      />

      <Card className="shadow-lg border-0">
        <CardHeader>
          <div className="flex items-center gap-3 mb-2">
            {currentStep === 1 && (
              <div className="bg-primary/10 p-2 rounded-full">
                <UserCircle className="h-6 w-6 text-primary" />
              </div>
            )}
            {currentStep === 2 && (
              <div className="bg-primary/10 p-2 rounded-full">
                <Briefcase className="h-6 w-6 text-primary" />
              </div>
            )}
            {currentStep === 3 && (
              <div className="bg-primary/10 p-2 rounded-full">
                <Laptop className="h-6 w-6 text-primary" />
              </div>
            )}
            {currentStep === 4 && (
              <div className="bg-primary/10 p-2 rounded-full">
                <FileCheck className="h-6 w-6 text-primary" />
              </div>
            )}
            <div>
              <CardTitle className="text-2xl font-semibold">
                {currentStep === 1 && "Personal Information"}
                {currentStep === 2 && "Job Details"}
                {currentStep === 3 && "IT Setup"}
                {currentStep === 4 && "Review & Submit"}
              </CardTitle>
              <CardDescription>
                {currentStep === 1 && "Tell us about the new employee"}
                {currentStep === 2 && "Enter job-related information"}
                {currentStep === 3 && "Configure IT equipment and access"}
                {currentStep === 4 && "Review all information before submitting"}
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <div className="step-content">
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <FormLabel htmlFor="firstName">First Name</FormLabel>
                    <Input
                      id="firstName"
                      placeholder="First Name"
                      value={formData.firstName || ''}
                      onChange={(e) => updateFormData('firstName', e.target.value)}
                      className="transition-all duration-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <FormLabel htmlFor="lastName">Last Name</FormLabel>
                    <Input
                      id="lastName"
                      placeholder="Last Name"
                      value={formData.lastName || ''}
                      onChange={(e) => updateFormData('lastName', e.target.value)}
                      className="transition-all duration-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <FormLabel htmlFor="email">Personal Email</FormLabel>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email || ''}
                      onChange={(e) => updateFormData('email', e.target.value)}
                      className="transition-all duration-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <FormLabel htmlFor="dob">Date of Birth</FormLabel>
                    <Input
                      id="dob"
                      type="date"
                      value={formData.dob || ''}
                      onChange={(e) => updateFormData('dob', e.target.value)}
                      className="transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <FormLabel htmlFor="department">Department</FormLabel>
                    <Select
                      onValueChange={(value) => updateFormData('department', value)}
                      defaultValue={formData.department || ''}
                    >
                      <SelectTrigger className="transition-all duration-200">
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="engineering">Engineering</SelectItem>
                        <SelectItem value="marketing">Marketing</SelectItem>
                        <SelectItem value="sales">Sales</SelectItem>
                        <SelectItem value="hr">Human Resources</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <FormLabel htmlFor="jobTitle">Job Title</FormLabel>
                    <Input
                      id="jobTitle"
                      placeholder="Job Title"
                      value={formData.jobTitle || ''}
                      onChange={(e) => updateFormData('jobTitle', e.target.value)}
                      className="transition-all duration-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <FormLabel htmlFor="startDate">Start Date</FormLabel>
                    <Input
                      id="startDate"
                      type="date"
                      value={formData.startDate || ''}
                      onChange={(e) => updateFormData('startDate', e.target.value)}
                      className="transition-all duration-200"
                    />
                  </div>

                  <div className="space-y-2">
                    <FormLabel htmlFor="manager">Manager</FormLabel>
                    <Input
                      id="manager"
                      placeholder="Manager Name"
                      value={formData.manager || ''}
                      onChange={(e) => updateFormData('manager', e.target.value)}
                      className="transition-all duration-200"
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <FormLabel htmlFor="deviceType">Device Type</FormLabel>
                    <Select
                      onValueChange={(value) => updateFormData('deviceType', value)}
                      defaultValue={formData.deviceType || 'laptop'}
                    >
                      <SelectTrigger className="transition-all duration-200">
                        <SelectValue placeholder="Select device" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="laptop">Laptop</SelectItem>
                        <SelectItem value="desktop">Desktop</SelectItem>
                        <SelectItem value="none">None</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <FormLabel htmlFor="phoneType">Phone Type</FormLabel>
                    <Select
                      onValueChange={(value) => updateFormData('phoneType', value)}
                      defaultValue={formData.phoneType || 'none'}
                    >
                      <SelectTrigger className="transition-all duration-200">
                        <SelectValue placeholder="Select phone" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="iphone">iPhone</SelectItem>
                        <SelectItem value="android">Android</SelectItem>
                        <SelectItem value="none">None</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="col-span-2">
                    <div className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm transition-all duration-200">
                      <div className="space-y-0.5">
                        <FormLabel>Microsoft 365</FormLabel>
                        <FormDescription>
                          Assign Microsoft 365 license
                        </FormDescription>
                      </div>
                      <Switch
                        checked={formData.needsM365 || false}
                        onCheckedChange={(checked) => updateFormData('needsM365', checked)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="rounded-lg border p-4 bg-muted/50">
                  <dl className="divide-y">
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Name:</dt>
                      <dd className="col-span-2">{formData.firstName} {formData.lastName}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Email:</dt>
                      <dd className="col-span-2">{formData.email}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Date of Birth:</dt>
                      <dd className="col-span-2">{formData.dob}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Department:</dt>
                      <dd className="col-span-2">{formData.department}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Job Title:</dt>
                      <dd className="col-span-2">{formData.jobTitle}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Start Date:</dt>
                      <dd className="col-span-2">{formData.startDate}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Manager:</dt>
                      <dd className="col-span-2">{formData.manager}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Device:</dt>
                      <dd className="col-span-2">{formData.deviceType}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Phone:</dt>
                      <dd className="col-span-2">{formData.phoneType}</dd>
                    </div>
                    <div className="grid grid-cols-3 py-2">
                      <dt className="font-medium">Microsoft 365:</dt>
                      <dd className="col-span-2">{formData.needsM365 ? 'Yes' : 'No'}</dd>
                    </div>
                  </dl>
                </div>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="px-6 py-4 bg-muted/20 flex justify-between">
          {currentStep > 1 ? (
            <Button
              variant="outline"
              onClick={handleBack}
              className="transition-all duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          ) : <div></div>}

          {currentStep < totalSteps ? (
            <Button
              onClick={handleNext}
              className="transition-all duration-200"
            >
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              className="bg-primary hover:bg-primary/90 transition-all duration-200"
            >
              Submit
              <Check className="ml-2 h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default OnboardingForm;
