/**
 * Utility functions for interacting with Dataverse
 */

import { TokenCredential } from "@azure/identity";
import axios from "axios";
import { InvocationContext } from "@azure/functions";

// Import the hardcoded config from employeeDataverse.ts
// or define it here if you prefer
const config = {
  dataverse: {
    environmentUrl: "https://orgbce2d577.crm.dynamics.com",
    apiVersion: "v9.2"
  }
};

/**
 * Status enum for employee records
 */
export enum EmployeeStatus {
  Draft = 1,
  Submitted = 2,
  InProgress = 3,
  Completed = 4,
  Rejected = 5
}

/**
 * Interface for the employee record in Dataverse
 */
export interface EmployeeRecord {
  id?: string;  // This is the Dataverse-generated ID
  firstName: string;
  lastName: string;
  email: string;
  workEmail?: string;
  personalEmail?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  startDate?: string;
  department?: string;
  jobTitle?: string;
  jobCategory?: string;
  branch?: string;
  physicalWorkLocation?: string;
  physicalWorkAddress?: string;
  hiringManagerFirstName?: string;
  hiringManagerLastName?: string;
  hiringManagerEmail?: string;
  isRehire?: boolean;
  hireType?: string;
  positionType?: string;
  replacedFirstName?: string;
  replacedLastName?: string;
  workSchedule?: string;
  hrNotes?: string;
  deviceType?: string;
  phoneType?: string;
  needsM365?: boolean;
  deskPhoneExt?: string;
  itDevices?: string[];
  itNotes?: string;
  submittedBy?: string;
  submittedAt?: string;
  status?: string | number; // Can be string for display or number for Dataverse
  processedBy?: string;
  processedAt?: string;
}

/**
 * Class for interacting with Dataverse
 */
export class DataverseClient {
  private credential: TokenCredential;
  private baseUrl: string;
  private logger: InvocationContext;
  private employeeEntitySetName: string = 'hirehub_employeeses'; // Default, will be updated if found

  /**
   * Constructor for DataverseClient
   * @param credential TokenCredential for authentication
   * @param logger Logger for logging messages
   */
  constructor(credential: TokenCredential, logger: InvocationContext) {
    this.credential = credential;
    this.logger = logger;
    
    // Get the base URL from config
    const environmentUrl = config.dataverse.environmentUrl;
    const apiVersion = config.dataverse.apiVersion;
    this.baseUrl = `${environmentUrl}/api/data/${apiVersion}`;
    
    this.logger.log(`Initialized DataverseClient with base URL: ${this.baseUrl}`);
  }

  /**
   * Get the entity set name for the employee table
   * @returns The entity set name (e.g., 'hirehub_employeeses')
   */
  getEmployeeEntitySetName(): string {
    return this.employeeEntitySetName;
  }

  /**
   * Get the access token for Dataverse
   * @returns Promise<string> Access token
   */
  private async getAccessToken(): Promise<string> {
    // Use the exact Dataverse environment URL as the audience/resource without /.default
    // This is important for Dataverse API access
    const resource = config.dataverse.environmentUrl.replace(/\/$/, '');
    this.logger.log(`Requesting token for resource: ${resource}`);

    try {
      const tokenResponse = await this.credential.getToken(resource);

      // Log token info (without exposing the actual token)
      if (tokenResponse && tokenResponse.token) {
        const tokenParts = tokenResponse.token.split('.');
        if (tokenParts.length >= 2) {
          try {
            // Only log the header part of the JWT to see what claims are being used
            const header = JSON.parse(Buffer.from(tokenParts[0], 'base64').toString());
            this.logger.log(`Token header: ${JSON.stringify(header)}`);
          } catch (e) {
            // Ignore parsing errors
          }
        }
      }

      return tokenResponse.token;
    } catch (error) {
      this.logger.error("Error getting access token:", error);
      throw error;
    }
  }

  /**
   * Get all entity definitions in the environment
   * @returns Promise<any[]> List of entity definitions
   */
  async getAllEntityDefinitions(): Promise<any[]> {
    try {
      const accessToken = await this.getAccessToken();
      
      this.logger.log('Getting all entity definitions to validate table names...');
      
      const response = await axios.get(
        `${this.baseUrl}/EntityDefinitions?$select=LogicalName,SchemaName,DisplayName,EntitySetName`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
          }
        }
      );
      
      if (response.status === 200 && response.data && response.data.value) {
        this.logger.log(`Retrieved ${response.data.value.length} entity definitions`);
        
        // Log all table names for debugging
        this.logger.log('All available tables:');
        const allTableNames = response.data.value.map((entity: any) => 
          `${entity.LogicalName} (EntitySetName: ${entity.EntitySetName})`
        );
        // Log in batches to avoid overwhelming the console
        const batchSize = 10;
        for (let i = 0; i < allTableNames.length; i += batchSize) {
          this.logger.log(allTableNames.slice(i, i + batchSize).join(', '));
        }
        
        // Check if our table exists in the list
        const employeeTable = response.data.value.find(
          (entity: any) => entity.LogicalName === 'hirehub_employees'
        );
        
        if (employeeTable) {
          this.logger.log(`Found hirehub_employees table in entity definitions! EntitySetName: ${employeeTable.EntitySetName}`);
          // Store the entity set name for later use
          this.employeeEntitySetName = employeeTable.EntitySetName;
        } else {
          this.logger.warn('hirehub_employees table NOT found in entity definitions!');
          // Log some similar table names to help identify potential naming issues
          const similarTables = response.data.value
            .filter((entity: any) => 
              entity.LogicalName.includes('hire') || 
              entity.LogicalName.includes('employee')
            )
            .map((entity: any) => `${entity.LogicalName} (EntitySetName: ${entity.EntitySetName})`);
          
          if (similarTables.length > 0) {
            this.logger.log('Similar tables found:', similarTables);
          }
        }
        
        return response.data.value;
      }
      
      return [];
    } catch (error) {
      this.logger.error('Error getting entity definitions:', error.message);
      if (error.response && error.response.data) {
        this.logger.error('Error response:', JSON.stringify(error.response.data, null, 2));
      }
      return [];
    }
  }

  /**
   * Validate the hirehub_employees table name
   * @returns Promise<boolean> True if the table name is valid
   */
  async validateTableName(): Promise<boolean> {
    try {
      const entities = await this.getAllEntityDefinitions();
      
      // Check if our table exists in the list
      const employeeTable = entities.find(
        (entity: any) => entity.LogicalName === 'hirehub_employees'
      );
      
      return !!employeeTable;
    } catch (error) {
      this.logger.error('Error validating table name:', error);
      return false;
    }
  }

  /**
   * Check if the employee table exists in Dataverse
   * @returns Promise<boolean> True if the table exists, false otherwise
   */
  async tableExists(): Promise<boolean> {
    const tableLogicalName = 'hirehub_employees';

    try {
      // First validate the table name against all entities
      const isValidName = await this.validateTableName();
      if (!isValidName) {
        this.logger.warn(`Table '${tableLogicalName}' not found in entity definitions. Check the table name.`);
      }

      const accessToken = await this.getAccessToken();

      this.logger.log(`Checking if table exists at: ${this.baseUrl}/EntityDefinitions(LogicalName='${tableLogicalName}')`);

      // Check if the table exists by querying the metadata
      const response = await axios.get(
        `${this.baseUrl}/EntityDefinitions(LogicalName='${tableLogicalName}')`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
          }
        }
      );

      if (response.status === 200) {
        this.logger.log(`Table '${tableLogicalName}' exists. Response:`, JSON.stringify(response.data, null, 2));
        return true;
      }

      this.logger.warn(`Unexpected response status ${response.status} when checking if table exists.`);
      return false;
    } catch (error) {
      if (error.response) {
        const status = error.response.status;

        if (status === 404) {
          this.logger.log(`Table '${tableLogicalName}' does not exist (404 response)`);
          return false; // Table doesn't exist
        }

        if (status === 403) {
          this.logger.warn(`Permission denied (403) when checking if table '${tableLogicalName}' exists. This may be expected if the table does not exist yet.`);
          return false; // Treat 403 as table doesn't exist for our purposes
        }

        if (status === 401) {
          this.logger.warn(`Authentication failed (401) when checking if table '${tableLogicalName}' exists. Token may be invalid or expired.`);
          return false; // Treat authentication failures as table doesn't exist
        }

        if (status === 400) {
          this.logger.warn(`Bad request (400) when checking if table '${tableLogicalName}' exists. This could be due to token scope issues.`);
          if (error.response.data && error.response.data.error) {
            this.logger.error('Error details:', JSON.stringify(error.response.data.error, null, 2));
          }
          return false; // Treat bad requests as table doesn't exist
        }

        this.logger.error(`Error response when checking if table '${tableLogicalName}' exists:`, error.response.data);
      } else if (error.request) {
        this.logger.error(`No response received when checking if table '${tableLogicalName}' exists:`, error.request);
      }

      this.logger.error(`Unexpected error checking if table '${tableLogicalName}' exists:`, error.message);

      // Instead of throwing, return false to indicate the table doesn't exist
      // This makes the code more resilient to transient errors
      return false;
    }
  }

  /**
   * Ensure the employee table exists in Dataverse
   * @returns Promise<void>
   */
  async ensureEmployeeTableExists(): Promise<void> {
    try {
      const tableLogicalName = 'hirehub_employees';

      try {
        // Check if the table exists
        const exists = await this.tableExists();
        if (exists) {
          this.logger.log(`Table '${tableLogicalName}' already exists.`);

          // Get entity definitions to validate table names and find the entity set name
          this.logger.log('Getting entity definitions to validate table names...');
          await this.getAllEntityDefinitions();

          // Check if all required columns exist
          this.logger.log('Ensuring all required columns exist...');
          await this.ensureRequiredColumnsExist();
          
          return;
        }
      } catch (err) {
        this.logger.error(`Error checking if table '${tableLogicalName}' exists:`, err);
        // Continue to try creating the table anyway
      }

      // Table doesn't exist, create it
      await this.createTable();
      
      // Publish customizations after creating the table
      await this.publishCustomizations();
    } catch (error) {
      this.logger.error('Error ensuring employee table exists:', error);
      throw error;
    }
  }

  /**
   * Create the employee table in Dataverse
   * @returns Promise<void>
   */
  async createTable(): Promise<void> {
    try {
      const accessToken = await this.getAccessToken();
      
      this.logger.log('Creating employee table in Dataverse...');
      
      // Create the entity definition with exact schema format for Dataverse API
      const entityDefinition = {
        "@odata.type": "Microsoft.Dynamics.CRM.EntityMetadata",
        SchemaName: 'hirehub_employees',
        DisplayName: {
          LocalizedLabels: [
            {
              Label: 'Employee',
              LanguageCode: 1033
            }
          ]
        },
        DisplayCollectionName: {
          LocalizedLabels: [
            {
              Label: 'Employees',
              LanguageCode: 1033
            }
          ]
        },
        Description: {
          LocalizedLabels: [
            {
              Label: 'Employee table for onboarding records',
              LanguageCode: 1033
            }
          ]
        },
        // Additional properties
        OwnershipType: 'UserOwned',
        HasActivities: false,
        HasNotes: false,
        IsActivity: false,
        // Define the primary name attribute as part of the Attributes array
        Attributes: [
          {
            "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
            AttributeType: "String",
            AttributeTypeName: { Value: "StringType" },
            SchemaName: "hirehub_name",
            DisplayName: {
              LocalizedLabels: [{ Label: "Name", LanguageCode: 1033 }]
            },
            Description: {
              LocalizedLabels: [{ Label: "Employee's full name", LanguageCode: 1033 }]
            },
            IsPrimaryName: true,
            RequiredLevel: { Value: "ApplicationRequired" },
            MaxLength: 100,
            FormatName: { Value: "Text" }
          }
        ]
      };

      // Log the entity definition for debugging
      this.logger.log('Entity definition payload:', JSON.stringify(entityDefinition, null, 2));

      try {
        // Create the entity
        this.logger.log(`Sending request to create table to: ${this.baseUrl}/EntityDefinitions`);
        this.logger.log(`Using entity definition: ${JSON.stringify(entityDefinition, null, 2)}`);

        // Skip the initial API check and directly try to create the entity
        this.logger.log('Skipping API check and directly attempting to create the entity...');

        // Now try to create the entity
        const createResponse = await axios.post(
          `${this.baseUrl}/EntityDefinitions`,
          entityDefinition,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
              Accept: 'application/json',
              'OData-MaxVersion': '4.0',
              'OData-Version': '4.0'
            }
          }
        );

        this.logger.log(`Entity creation response status: ${createResponse.status}`);

        if (createResponse.data) {
          this.logger.log(`Entity creation response data: ${JSON.stringify(createResponse.data, null, 2)}`);
        }

        // Wait a moment for the entity to be fully created
        this.logger.log('Waiting for entity to be fully created...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Create the attributes (columns)
        await this.createTableColumns(accessToken);

        this.logger.log('Employee table created successfully');
      } catch (error) {
        if (error.response) {
          const status = error.response.status;

          this.logger.error(`Error creating table. Status: ${status}`);

          if (error.response.data) {
            this.logger.error('Error response data:', JSON.stringify(error.response.data, null, 2));
          }

          if (status === 403) {
            this.logger.warn('Permission denied when creating table. Your app needs the System Customizer role in Dataverse.');
            this.logger.warn('Using mock data instead. Please create an application user in Dataverse and assign it the System Customizer role.');
            // Continue with mock data instead of throwing an error
            return;
          }
          else if (status === 400) {
            this.logger.warn('Bad request when creating table. This could be due to:');
            this.logger.warn('1. The table already exists but you don\'t have permission to see it');
            this.logger.warn('2. Invalid entity definition format');
            this.logger.warn('3. Token scope issues - ensure you have proper permissions');

            if (error.response.data && error.response.data.error) {
              this.logger.error('Error details:', JSON.stringify(error.response.data.error, null, 2));

              // Check if the error is because the table already exists
              const errorMessage = error.response.data.error.message || '';
              if (errorMessage.includes('already exists') || errorMessage.includes('duplicate')) {
                this.logger.log('Table already exists based on error message. Continuing...');
                return; // Table already exists, so we can continue
              }
            }

            this.logger.warn('Using mock data instead.');
            // Continue with mock data instead of throwing an error
            return;
          }
          else if (status === 401) {
            this.logger.warn('Authentication failed. Token may be invalid or expired.');
            this.logger.warn('Using mock data instead.');
            return;
          }
          else if (status === 404) {
            this.logger.warn('Resource not found (404). The EntityDefinitions endpoint might not be accessible or the URL is incorrect.');
            this.logger.warn(`Attempted URL: ${this.baseUrl}/EntityDefinitions`);
            this.logger.warn('Using mock data instead.');
            return;
          }
        } else if (error.request) {
          this.logger.error('No response received from server:', error.request);
        }

        this.logger.error('Unexpected error creating table:', error);
        this.logger.error('Error message:', error.message);
        if (error.stack) {
          this.logger.error('Error stack:', error.stack);
        }

        // Continue with mock data instead of throwing an error
        this.logger.warn('Using mock data due to unexpected error.');
        return;
      }
    } catch (error) {
      this.logger.error('Error creating employee table:', error);
      throw error;
    }
  }

  /**
   * Create the columns for the employee table
   * @param accessToken Access token for Dataverse
   * @returns Promise<void>
   */
  private async createTableColumns(accessToken: string): Promise<void> {
    // If we're in mock mode (due to 403 errors), just return
    if (!accessToken) {
      this.logger.warn('Skipping column creation due to permission issues');
      return;
    }

    // Define the columns to create
    // Note: hirehub_fullname is already created as the primary attribute
    // so we don't need to create it again
    this.logger.log('Creating columns for hirehub_employees table...');

    const columns = [
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_firstname',
        DisplayName: {
          LocalizedLabels: [{ Label: 'First Name', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Employee\'s first name', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "ApplicationRequired" }, // Required by application
        MaxLength: 100,
        FormatName: { Value: 'Text' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_lastname',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Last Name', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Employee\'s last name', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "ApplicationRequired" }, // Required by application
        MaxLength: 100,
        FormatName: { Value: 'Text' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_email',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Email', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Employee\'s email address', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "ApplicationRequired" }, // Required by application
        MaxLength: 100,
        FormatName: { Value: 'Email' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_workemail',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Work Email', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Employee\'s work email address', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        MaxLength: 100,
        FormatName: { Value: 'Email' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.DateTimeAttributeMetadata",
        AttributeType: 'DateTime',
        SchemaName: 'hirehub_startdate',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Start Date', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Employee\'s start date', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        DateTimeBehavior: { Value: 'DateOnly' },
        Format: "DateOnly" // Changed from { Value: "DateOnly" } to "DateOnly"
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_department',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Department', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Employee\'s department', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        MaxLength: 100,
        FormatName: { Value: 'Text' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_jobtitle',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Job Title', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Employee\'s job title', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        MaxLength: 100,
        FormatName: { Value: 'Text' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.BooleanAttributeMetadata",
        AttributeType: 'Boolean',
        SchemaName: 'hirehub_needsm365',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Needs M365', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Indicates if the employee needs Microsoft 365', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        DefaultValue: true,
        // Add the required OptionSet with TrueOption and FalseOption
        OptionSet: {
          TrueOption: {
            Label: { LocalizedLabels: [{ Label: "Yes", LanguageCode: 1033 }] },
            Value: 1
          },
          FalseOption: {
            Label: { LocalizedLabels: [{ Label: "No", LanguageCode: 1033 }] },
            Value: 0
          }
        }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_devicetype',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Device Type', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Type of device needed', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        MaxLength: 100,
        FormatName: { Value: 'Text' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.PicklistAttributeMetadata",
        AttributeType: 'Picklist',
        SchemaName: 'hirehub_status',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Status', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Status of the onboarding process', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        OptionSet: {
          IsGlobal: false,
          OptionSetType: 'Picklist',
          Options: [
            { Value: 1, Label: { LocalizedLabels: [{ Label: 'Draft', LanguageCode: 1033 }] } },
            { Value: 2, Label: { LocalizedLabels: [{ Label: 'Submitted', LanguageCode: 1033 }] } },
            { Value: 3, Label: { LocalizedLabels: [{ Label: 'In Progress', LanguageCode: 1033 }] } },
            { Value: 4, Label: { LocalizedLabels: [{ Label: 'Completed', LanguageCode: 1033 }] } },
            { Value: 5, Label: { LocalizedLabels: [{ Label: 'Rejected', LanguageCode: 1033 }] } }
          ]
        }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_submittedby',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Submitted By', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Email of the person who submitted the form', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        MaxLength: 100,
        FormatName: { Value: 'Email' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.DateTimeAttributeMetadata",
        AttributeType: 'DateTime',
        SchemaName: 'hirehub_submittedat',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Submitted At', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Date and time when the form was submitted', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        DateTimeBehavior: { Value: 'UserLocal' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
        AttributeType: 'String',
        SchemaName: 'hirehub_processedby',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Processed By', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Email of the person who processed the form', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        MaxLength: 100,
        FormatName: { Value: 'Email' }
      },
      {
        "@odata.type": "Microsoft.Dynamics.CRM.DateTimeAttributeMetadata",
        AttributeType: 'DateTime',
        SchemaName: 'hirehub_processedat',
        DisplayName: {
          LocalizedLabels: [{ Label: 'Processed At', LanguageCode: 1033 }]
        },
        Description: {
          LocalizedLabels: [{ Label: 'Date and time when the form was processed', LanguageCode: 1033 }]
        },
        RequiredLevel: { Value: "None" }, // Optional
        DateTimeBehavior: { Value: 'UserLocal' }
      }
    ];

    // Create each column
    for (const column of columns) {
      try {
        this.logger.log(`Creating column: ${column.SchemaName} for hirehub_employees table`);
        
        const response = await axios.post(
          `${this.baseUrl}/EntityDefinitions(LogicalName='hirehub_employees')/Attributes`,
          column,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
              Accept: 'application/json',
              'OData-MaxVersion': '4.0',
              'OData-Version': '4.0'
            }
          }
        );

        this.logger.log(`Created column: ${column.SchemaName}, Status: ${response.status}`);
      } catch (error) {
        if (error.response) {
          const status = error.response.status;

          if (status === 500) {
            this.logger.error(`Server error (500) when creating column ${column.SchemaName}.`);
            this.logger.error(`Column definition: ${JSON.stringify(column, null, 2)}`);
            if (error.response.data && error.response.data.error) {
              this.logger.error('Error details:', JSON.stringify(error.response.data.error, null, 2));
            }
            // Continue with the next column
            continue;
          }

          if (status === 403) {
            this.logger.warn(`Permission denied (403) when creating column ${column.SchemaName}. Your app needs the System Customizer role in Dataverse.`);
            // Continue with the next column instead of throwing an error
            continue;
          }

          if (status === 400) {
            this.logger.warn(`Bad request (400) when creating column ${column.SchemaName}. This could be due to invalid schema or the column already exists.`);
            if (error.response.data && error.response.data.error) {
              this.logger.error('Error details:', JSON.stringify(error.response.data.error, null, 2));
            }
            // Continue with the next column
            continue;
          }

          if (status === 401) {
            this.logger.warn(`Authentication failed (401) when creating column ${column.SchemaName}. Token may be invalid or expired.`);
            // Continue with the next column
            continue;
          }
        }

        this.logger.error(`Error creating column ${column.SchemaName}:`, error);
        if (error.response && error.response.data) {
          this.logger.error('Response data:', JSON.stringify(error.response.data, null, 2));
        }
        // Continue with other columns instead of failing completely
        continue;
      }
    }
  }

  /**
   * Create or update an employee record in Dataverse using UPSERT pattern
   * @param employee Employee record to create or update
   * @returns Promise<EmployeeRecord> Created or updated employee record
   */
  async createEmployee(employee: EmployeeRecord): Promise<EmployeeRecord> {
    try {
      const accessToken = await this.getAccessToken();
      const entitySetName = this.getEmployeeEntitySetName();

      this.logger.log(`Creating/updating employee record for ${employee.firstName} ${employee.lastName}`);
      this.logger.log(`Using entity set name: ${entitySetName}`);

      // Format the start date correctly for Edm.Date type (YYYY-MM-DD)
      let formattedStartDate = null;
      if (employee.startDate) {
        // Parse the date and format it as YYYY-MM-DD
        const date = new Date(employee.startDate);
        formattedStartDate = date.toISOString().split('T')[0]; // Get only the date part
        this.logger.log(`Formatted start date from ${employee.startDate} to ${formattedStartDate}`);
      }

      // Convert status string to numeric value if needed
      let statusValue = EmployeeStatus.Submitted; // Default to Submitted (2)
      if (employee.status) {
        if (typeof employee.status === 'string') {
          // Convert string status to numeric value
          switch (employee.status.toLowerCase()) {
            case 'draft':
              statusValue = EmployeeStatus.Draft;
              break;
            case 'submitted':
              statusValue = EmployeeStatus.Submitted;
              break;
            case 'in_progress':
            case 'inprogress':
            case 'in progress':
              statusValue = EmployeeStatus.InProgress;
              break;
            case 'completed':
              statusValue = EmployeeStatus.Completed;
              break;
            case 'rejected':
              statusValue = EmployeeStatus.Rejected;
              break;
            default:
              statusValue = EmployeeStatus.Submitted; // Default
          }
        } else if (typeof employee.status === 'number') {
          // Use the numeric value directly
          statusValue = employee.status;
        }
      }

      // Map our EmployeeRecord to Dataverse entity format
      const dataverseRecord: any = {
        // Primary name field - use hirehub_employees_name instead of hirehub_name
        hirehub_employees_name: `${employee.firstName} ${employee.lastName}`,

        // Include all other attributes with their correct column names
        hirehub_firstname: employee.firstName,
        hirehub_lastname: employee.lastName,
        hirehub_email: employee.email,
        hirehub_workemail: employee.workEmail || '',
        hirehub_startdate: formattedStartDate, // Use the formatted date
        hirehub_department: employee.department || '',
        hirehub_jobtitle: employee.jobTitle || '',
        hirehub_needsm365: employee.needsM365 === true,
        hirehub_devicetype: employee.deviceType || '',
        hirehub_submittedby: employee.submittedBy || '',
        hirehub_submittedat: employee.submittedAt || new Date().toISOString(),
        hirehub_status: statusValue // Use the numeric value
      };

      this.logger.log('Prepared dataverse record:', JSON.stringify(dataverseRecord, null, 2));

      try {

        // If employee has a Dataverse ID (GUID), try to get it directly
        if (employee.id && employee.id.trim() !== '') {
          this.logger.log(`Employee has Dataverse ID: ${employee.id}. Attempting direct lookup...`);

          try {
            // Try to get the record directly by ID
            const response = await axios.get(
              `${this.baseUrl}/${entitySetName}(${employee.id})`,
              {
                headers: {
                  Authorization: `Bearer ${accessToken}`,
                  Accept: 'application/json',
                  'OData-MaxVersion': '4.0',
                  'OData-Version': '4.0'
                }
              }
            );

            if (response.status === 200 && response.data) {
              this.logger.log(`Found existing record with Dataverse ID: ${employee.id}. Updating...`);

              // Update the existing record using PATCH
              const updateResponse = await axios.patch(
                `${this.baseUrl}/${entitySetName}(${employee.id})`,
                dataverseRecord,
                {
                  headers: {
                    Authorization: `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                    'OData-MaxVersion': '4.0',
                    'OData-Version': '4.0',
                    'If-Match': '*' // Allow update regardless of etag
                  }
                }
              );

              this.logger.log(`Successfully updated record. Status: ${updateResponse.status}`);

              // Return the updated record
              return {
                ...employee,
                submittedAt: new Date().toISOString(),
                status: 'updated'
              };
            }
          } catch (lookupError) {
            // If we get a 404, the record doesn't exist with this ID
            if (lookupError.response && lookupError.response.status === 404) {
              this.logger.log(`No record found with Dataverse ID: ${employee.id}. Will create a new record.`);
            } else {
              // For other errors, log and continue with email lookup as fallback
              this.logger.warn(`Error looking up record by Dataverse ID: ${lookupError.message}`);
            }
          }
        }

        // Important fallback: Query for existing record with the same email
        // This prevents duplicate records for the same person
        this.logger.log(`Checking if employee with email ${employee.email} already exists...`);
        const queryUrl = `${this.baseUrl}/${entitySetName}?$filter=hirehub_email eq '${encodeURIComponent(employee.email)}'`;
        this.logger.log(`Querying for existing record by email: ${queryUrl}`);

        const queryResponse = await axios.get(
          queryUrl,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              Accept: 'application/json',
              'OData-MaxVersion': '4.0',
              'OData-Version': '4.0'
            }
          }
        );

        // Check if we found an existing record
        if (queryResponse.data && queryResponse.data.value && queryResponse.data.value.length > 0) {
          // Record exists - update it
          const existingRecord = queryResponse.data.value[0];
          const recordId = existingRecord.hirehub_employeesid;

          this.logger.log(`Found existing record with email: ${employee.email}, Dataverse ID: ${recordId}. Updating...`);

          // Update the existing record using PATCH
          const updateResponse = await axios.patch(
            `${this.baseUrl}/${entitySetName}(${recordId})`,
            dataverseRecord,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                Accept: 'application/json',
                'OData-MaxVersion': '4.0',
                'OData-Version': '4.0',
                'If-Match': '*' // Allow update regardless of etag
              }
            }
          );

          this.logger.log(`Successfully updated record. Status: ${updateResponse.status}`);

          // Return the updated record
          return {
            ...employee,
            id: recordId,
            submittedAt: new Date().toISOString(),
            status: 'updated'
          };
        } else {
          // Record doesn't exist - create a new one
          this.logger.log('No existing record found. Creating new record...');

          // Create a new record using POST
          const createResponse = await axios.post(
            `${this.baseUrl}/${entitySetName}`,
            dataverseRecord,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                Accept: 'application/json',
                'OData-MaxVersion': '4.0',
                'OData-Version': '4.0'
              }
            }
          );

          this.logger.log(`Successfully created record. Status: ${createResponse.status}`);

          // Get the ID from the response
          let id = '';
          if (createResponse.headers && createResponse.headers.location) {
            // Extract ID from the location header
            const locationParts = createResponse.headers.location.split('(');
            if (locationParts.length > 1) {
              id = locationParts[1].replace(')', '').replace(/'/g, '');
            }
          }

          // Return the created record with the Dataverse ID
          // The employeeID will be included if it was provided
          return {
            ...employee,
            id: id || `emp-${Date.now()}`,
            submittedAt: new Date().toISOString(),
            status: 'created'
          };
        }
      } catch (error) {
        this.logger.error(`Error in UPSERT operation: ${error.message}`);
        if (error.response && error.response.data) {
          this.logger.error(`Error response data: ${JSON.stringify(error.response.data, null, 2)}`);
        }

        if (error.response) {
          const status = error.response.status;

          this.logger.error(`Error creating employee record. Status: ${status}`);

          if (error.response.data) {
            this.logger.error('Error response data:', JSON.stringify(error.response.data, null, 2));
          }

          if (status === 403 || status === 401) {
            this.logger.warn(`Permission denied (${status}) when creating employee record. Falling back to mock data.`);
            // Fall back to mock data
            return {
              ...employee,
              id: `emp-${Date.now()}`,
              submittedAt: new Date().toISOString(),
              status: 'submitted'
            };
          }

          if (status === 400) {
            this.logger.warn('Bad request (400) when creating employee record. This could be due to invalid data format.');
            if (error.response.data && error.response.data.error) {
              this.logger.error('Error details:', JSON.stringify(error.response.data.error, null, 2));
            }
            // Fall back to mock data
            return {
              ...employee,
              id: `emp-${Date.now()}`,
              submittedAt: new Date().toISOString(),
              status: 'submitted'
            };
          }

          if (status === 404) {
            this.logger.warn('Not Found (404) when creating employee record. The table might not exist or the URL is incorrect.');
            this.logger.warn(`Attempted URL: ${this.baseUrl}/${entitySetName}`);

            // Fall back to mock data
            this.logger.warn('Returning mock data since the table appears to not exist or is inaccessible.');
            return {
              ...employee,
              id: `emp-${Date.now()}`,
              submittedAt: new Date().toISOString(),
              status: 'submitted'
            };
          }
        } else if (error.request) {
          this.logger.error('No response received from server:', error.request);
        }

        // For other errors, log and return mock data
        this.logger.error("Error creating employee record:", error.message);
        if (error.stack) {
          this.logger.error("Error stack:", error.stack);
        }

        // Fall back to mock data for all errors
        return {
          ...employee,
          id: `emp-${Date.now()}`,
          submittedAt: new Date().toISOString(),
          status: 'submitted'
        };
      }
    } catch (error) {
      this.logger.error("Error in createEmployee method:", error);
      throw error;
    }
  }

  /**
   * Get employee records from Dataverse
   * @param filter Optional filter for the query
   * @returns Promise<EmployeeRecord[]> List of employee records
   */
  async getEmployees(filter?: string): Promise<EmployeeRecord[]> {
    try {
      const accessToken = await this.getAccessToken();
      const entitySetName = this.getEmployeeEntitySetName();

      this.logger.log("Getting employee records from Dataverse");
      this.logger.log(`Using entity set name: ${entitySetName}`);

      try {
        // First check if the table exists
        const tableExists = await this.tableExists();
        this.logger.log(`Table exists check result: ${tableExists}`);

        if (!tableExists) {
          this.logger.warn('Employee table does not exist. Returning mock data.');
          const mockData = this.getMockEmployees();
          this.logger.log(`Returning ${mockData.length} mock employee records`);
          return mockData;
        }

        // Use the correct endpoint with the entity set name
        let url = `${this.baseUrl}/${entitySetName}`;

        // Add filter if provided
        if (filter) {
          url += `?$filter=${encodeURIComponent(filter)}`;
        }

        this.logger.log(`Getting employees from endpoint: ${url}`);

        // Make the request to Dataverse
        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
          }
        });

        this.logger.log(`Dataverse response status: ${response.status}`);
        
        if (response.status === 200 && response.data && response.data.value) {
          this.logger.log(`Retrieved ${response.data.value.length} records from Dataverse`);
          
          // If no records, log that specifically
          if (response.data.value.length === 0) {
            this.logger.warn('No records found in Dataverse table');
          }
          
          // Map Dataverse records to our EmployeeRecord format
          return response.data.value.map((record: any) => this.mapDataverseToEmployeeRecord(record));
        } else {
          this.logger.warn(`Unexpected response from Dataverse: ${response.status}. Falling back to mock data.`);
          return this.getMockEmployees();
        }
      } catch (error) {
        if (error.response) {
          const status = error.response.status;

          if (status === 403 || status === 401) {
            this.logger.warn(`Permission denied (${status}) when getting employee records. Falling back to mock data.`);
            return this.getMockEmployees();
          }

          if (status === 404) {
            this.logger.warn('Table not found (404) when getting employee records. Falling back to mock data.');
            return this.getMockEmployees();
          }
        }

        this.logger.error("Error getting employee records:", error);
        // Fall back to mock data for any error
        return this.getMockEmployees();
      }
    } catch (error) {
      this.logger.error("Error in getEmployees method:", error);
      return this.getMockEmployees();
    }
  }

  /**
   * Get mock employee records for fallback
   * @returns Array of mock employee records
   */
  private getMockEmployees(): EmployeeRecord[] {
    return [
      {
        id: "emp-123456",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        workEmail: "<EMAIL>",
        department: "Marketing",
        jobTitle: "Marketing Specialist",
        startDate: "2025-05-01",
        submittedAt: "2025-04-01T12:00:00Z",
        status: "submitted"
      },
      {
        id: "emp-123457",
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        workEmail: "<EMAIL>",
        department: "IT",
        jobTitle: "Systems Administrator",
        startDate: "2025-05-15",
        submittedAt: "2025-04-02T14:30:00Z",
        status: "in_progress"
      }
    ];
  }

  /**
   * Map a Dataverse record to our EmployeeRecord format
   * @param record Dataverse record
   * @returns Mapped EmployeeRecord
   */
  private mapDataverseToEmployeeRecord(record: any): EmployeeRecord {
    // Extract full name if available (using the correct primary name field)
    let fullName = record.hirehub_employees_name || '';

    // Parse first and last name from full name if they're not directly available
    let firstName = record.hirehub_firstname || '';
    let lastName = record.hirehub_lastname || '';

    if (!firstName && !lastName && fullName) {
      const nameParts = fullName.split(' ');
      if (nameParts.length >= 2) {
        firstName = nameParts[0];
        lastName = nameParts.slice(1).join(' ');
      } else {
        firstName = fullName;
      }
    }

    // Get the ID from the record - prioritize the Dataverse ID field
    const id = record.hirehub_employeesid || record.id || `emp-${Date.now()}`;

    this.logger.log(`Mapping Dataverse record with ID: ${id}`);

    // Convert numeric status to string for display
    let statusString = 'unknown';
    if (record.hirehub_status !== undefined && record.hirehub_status !== null) {
      switch (record.hirehub_status) {
        case EmployeeStatus.Draft:
          statusString = 'draft';
          break;
        case EmployeeStatus.Submitted:
          statusString = 'submitted';
          break;
        case EmployeeStatus.InProgress:
          statusString = 'in_progress';
          break;
        case EmployeeStatus.Completed:
          statusString = 'completed';
          break;
        case EmployeeStatus.Rejected:
          statusString = 'rejected';
          break;
        default:
          statusString = 'unknown';
      }
    }

    return {
      id: id,
      firstName: firstName,
      lastName: lastName,
      email: record.hirehub_email || '',
      workEmail: record.hirehub_workemail || '',
      department: record.hirehub_department || '',
      jobTitle: record.hirehub_jobtitle || '',
      startDate: record.hirehub_startdate || '',
      submittedAt: record.hirehub_submittedat || '',
      submittedBy: record.hirehub_submittedby || '',
      processedAt: record.hirehub_processedat || '',
      processedBy: record.hirehub_processedby || '',
      needsM365: record.hirehub_needsm365 === true,
      deviceType: record.hirehub_devicetype || '',
      status: statusString
    };
  }

  /**
   * Get an employee record by ID
   * @param id Employee ID
   * @returns Promise<EmployeeRecord> Employee record
   */
  async getEmployeeById(id: string): Promise<EmployeeRecord> {
    try {
      const accessToken = await this.getAccessToken();
      const entitySetName = this.getEmployeeEntitySetName();

      this.logger.log(`Getting employee record for ID: ${id} from Dataverse`);

      try {
        // First check if the table exists
        const tableExists = await this.tableExists();

        if (!tableExists) {
          this.logger.warn('Employee table does not exist. Returning mock data.');
          return this.getMockEmployeeById(id);
        }

        // Use the correct endpoint with the entity set name
        const endpoint = `${this.baseUrl}/${entitySetName}(${id})`;
        this.logger.log(`Getting employee by ID from endpoint: ${endpoint}`);

        // Make the request to Dataverse
        const response = await axios.get(
          endpoint,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              Accept: 'application/json',
              'OData-MaxVersion': '4.0',
              'OData-Version': '4.0'
            }
          }
        );

        if (response.status === 200 && response.data) {
          // Map Dataverse record to our EmployeeRecord format
          return this.mapDataverseToEmployeeRecord(response.data);
        } else {
          this.logger.warn(`Unexpected response from Dataverse: ${response.status}. Falling back to mock data.`);
          return this.getMockEmployeeById(id);
        }
      } catch (error) {
        if (error.response) {
          const status = error.response.status;

          if (status === 403 || status === 401) {
            this.logger.warn(`Permission denied (${status}) when getting employee record. Falling back to mock data.`);
            return this.getMockEmployeeById(id);
          }

          if (status === 404) {
            this.logger.warn(`Employee record with ID ${id} not found. Falling back to mock data.`);
            return this.getMockEmployeeById(id);
          }
        }

        this.logger.error(`Error getting employee record for ID ${id}:`, error);
        // Fall back to mock data for any error
        return this.getMockEmployeeById(id);
      }
    } catch (error) {
      this.logger.error(`Error in getEmployeeById method for ID ${id}:`, error);
      return this.getMockEmployeeById(id);
    }
  }

  /**
   * Get a mock employee record by ID for fallback
   * @param id Employee ID
   * @returns Mock employee record
   */
  private getMockEmployeeById(id: string): EmployeeRecord {
    return {
      id: id,
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      workEmail: "<EMAIL>",
      department: "Marketing",
      jobTitle: "Marketing Specialist",
      startDate: "2025-05-01",
      submittedAt: "2025-04-01T12:00:00Z",
      status: "submitted"
    };
  }

  /**
   * Update an employee record in Dataverse
   * @param id Employee ID
   * @param employee Updated employee data
   * @returns Promise<EmployeeRecord> Updated employee record
   */
  async updateEmployee(id: string, employee: Partial<EmployeeRecord>): Promise<EmployeeRecord> {
    try {
      const accessToken = await this.getAccessToken();
      const entitySetName = this.getEmployeeEntitySetName();

      this.logger.log(`Updating employee record for ID: ${id} in Dataverse`);

      try {
        // First check if the table exists
        const tableExists = await this.tableExists();

        if (!tableExists) {
          this.logger.warn('Employee table does not exist. Returning mock data.');
          return this.getMockUpdatedEmployee(id, employee);
        }

        // Get the current record to merge with updates
        let currentRecord: EmployeeRecord;
        try {
          currentRecord = await this.getEmployeeById(id);
        } catch (error) {
          this.logger.warn(`Could not retrieve current employee record for ID ${id}. Using base record.`);
          // Create a base record with required fields
          currentRecord = {
            id: id,
            firstName: employee.firstName || "Unknown",
            lastName: employee.lastName || "Unknown",
            email: employee.email || "<EMAIL>"
          };
        }

        // Map our EmployeeRecord to Dataverse entity format
        const dataverseRecord: any = {};

        // Only include fields that are being updated
        if (employee.firstName !== undefined || employee.lastName !== undefined) {
          // Update the primary name field if either first or last name changes
          const firstName = employee.firstName !== undefined ? employee.firstName : currentRecord.firstName;
          const lastName = employee.lastName !== undefined ? employee.lastName : currentRecord.lastName;
          dataverseRecord.hirehub_employees_name = `${firstName} ${lastName}`;
        }

        if (employee.firstName !== undefined) dataverseRecord.hirehub_firstname = employee.firstName;
        if (employee.lastName !== undefined) dataverseRecord.hirehub_lastname = employee.lastName;
        if (employee.email !== undefined) dataverseRecord.hirehub_email = employee.email;
        if (employee.workEmail !== undefined) dataverseRecord.hirehub_workemail = employee.workEmail;
        
        // Format the start date correctly for Edm.Date type (YYYY-MM-DD)
        if (employee.startDate !== undefined) {
          if (employee.startDate) {
            // Parse the date and format it as YYYY-MM-DD
            const date = new Date(employee.startDate);
            dataverseRecord.hirehub_startdate = date.toISOString().split('T')[0]; // Get only the date part
            this.logger.log(`Formatted start date from ${employee.startDate} to ${dataverseRecord.hirehub_startdate}`);
          } else {
            dataverseRecord.hirehub_startdate = null;
          }
        }
        
        if (employee.department !== undefined) dataverseRecord.hirehub_department = employee.department;
        if (employee.jobTitle !== undefined) dataverseRecord.hirehub_jobtitle = employee.jobTitle;
        if (employee.needsM365 !== undefined) dataverseRecord.hirehub_needsm365 = employee.needsM365;
        if (employee.deviceType !== undefined) dataverseRecord.hirehub_devicetype = employee.deviceType;
        
        // Convert status string to numeric value if needed
        if (employee.status !== undefined) {
          if (typeof employee.status === 'string') {
            // Convert string status to numeric value
            switch (employee.status.toLowerCase()) {
              case 'draft':
                dataverseRecord.hirehub_status = EmployeeStatus.Draft;
                break;
              case 'submitted':
                dataverseRecord.hirehub_status = EmployeeStatus.Submitted;
                break;
              case 'in_progress':
              case 'inprogress':
              case 'in progress':
                dataverseRecord.hirehub_status = EmployeeStatus.InProgress;
                break;
              case 'completed':
                dataverseRecord.hirehub_status = EmployeeStatus.Completed;
                break;
              case 'rejected':
                dataverseRecord.hirehub_status = EmployeeStatus.Rejected;
                break;
              default:
                // Don't update if we don't recognize the status
                this.logger.warn(`Unrecognized status value: ${employee.status}. Skipping status update.`);
            }
          } else if (typeof employee.status === 'number') {
            // Use the numeric value directly
            dataverseRecord.hirehub_status = employee.status;
          }
        }
        
        if (employee.processedBy !== undefined) dataverseRecord.hirehub_processedby = employee.processedBy;

        // Always update the processed timestamp when updating
        dataverseRecord.hirehub_processedat = new Date().toISOString();

        // Update the record in Dataverse
        const response = await axios.patch(
          `${this.baseUrl}/${entitySetName}(${id})`,
          dataverseRecord,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
              Accept: 'application/json',
              'OData-MaxVersion': '4.0',
              'OData-Version': '4.0'
            }
          }
        );

        if (response.status === 204) {
          this.logger.log('Employee record updated successfully in Dataverse');

          // Merge the current record with the updates
          const updatedRecord = { ...currentRecord, ...employee };

          // Add metadata
          updatedRecord.processedAt = new Date().toISOString();

          return updatedRecord;
        } else {
          throw new Error(`Unexpected response status: ${response.status}`);
        }
      } catch (error) {
        if (error.response) {
          const status = error.response.status;

          if (status === 403 || status === 401) {
            this.logger.warn(`Permission denied (${status}) when updating employee record. Falling back to mock data.`);
            return this.getMockUpdatedEmployee(id, employee);
          }

          if (status === 404) {
            this.logger.warn(`Employee record with ID ${id} not found. Falling back to mock data.`);
            return this.getMockUpdatedEmployee(id, employee);
          }

          if (status === 400) {
            this.logger.warn('Bad request (400) when updating employee record. This could be due to invalid data format.');
            if (error.response.data && error.response.data.error) {
              this.logger.error('Error details:', error.response.data.error);
            }
            return this.getMockUpdatedEmployee(id, employee);
          }
        }

        this.logger.error(`Error updating employee record for ID ${id}:`, error);
        // Fall back to mock data for any error
        return this.getMockUpdatedEmployee(id, employee);
      }
    } catch (error) {
      this.logger.error(`Error in updateEmployee method for ID ${id}:`, error);
      return this.getMockUpdatedEmployee(id, employee);
    }
  }

  /**
   * Get a mock updated employee record for fallback
   * @param id Employee ID
   * @param employee Partial employee data for update
   * @returns Mock updated employee record
   */
  private getMockUpdatedEmployee(id: string, employee: Partial<EmployeeRecord>): EmployeeRecord {
    // Create a base record with required fields
    const baseRecord: EmployeeRecord = {
      id: id,
      firstName: employee.firstName || "John",
      lastName: employee.lastName || "Doe",
      email: employee.email || "<EMAIL>"
    };

    // Merge with employee data
    const updatedRecord = { ...baseRecord, ...employee };

    // Add metadata
    updatedRecord.processedAt = new Date().toISOString();

    return updatedRecord;
  }

  /**
   * Check if a specific column exists in the employee table
   * @param columnName The logical name of the column to check
   * @returns Promise<boolean> True if the column exists
   */
  async columnExists(columnName: string): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();
      
      this.logger.log(`Checking if column '${columnName}' exists in hirehub_employees table...`);
      
      const response = await axios.get(
        `${this.baseUrl}/EntityDefinitions(LogicalName='hirehub_employees')/Attributes(LogicalName='${columnName}')`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
          }
        }
      );
      
      if (response.status === 200) {
        this.logger.log(`Column '${columnName}' exists in hirehub_employees table.`);
        return true;
      }
      
      return false;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        this.logger.warn(`Column '${columnName}' does not exist in hirehub_employees table.`);
        return false;
      }
      
      this.logger.error(`Error checking if column '${columnName}' exists:`, error.message);
      return false;
    }
  }

  /**
   * Create a boolean column in the employee table
   * @param columnName The logical name of the column to create
   * @param displayName The display name of the column
   * @param description The description of the column
   * @param defaultValue The default value of the column
   * @returns Promise<boolean> True if the column was created successfully
   */
  async createBooleanColumn(columnName: string, displayName: string, description: string = "", defaultValue: boolean = false): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();
      
      this.logger.log(`Creating boolean column '${columnName}' in hirehub_employees table...`);
      
      const column = {
        "@odata.type": "Microsoft.Dynamics.CRM.BooleanAttributeMetadata",
        SchemaName: columnName,
        DisplayName: {
          LocalizedLabels: [
            {
              Label: displayName,
              LanguageCode: 1033
            }
          ]
        },
        Description: {
          LocalizedLabels: [
            {
              Label: description,
              LanguageCode: 1033
            }
          ]
        },
        RequiredLevel: {
          Value: "None"
        },
        DefaultValue: defaultValue,
        // Add the required OptionSet with TrueOption and FalseOption
        OptionSet: {
          TrueOption: {
            Label: { LocalizedLabels: [{ Label: "Yes", LanguageCode: 1033 }] },
            Value: 1
          },
          FalseOption: {
            Label: { LocalizedLabels: [{ Label: "No", LanguageCode: 1033 }] },
            Value: 0
          }
        }
      };
      
      const response = await axios.post(
        `${this.baseUrl}/EntityDefinitions(LogicalName='hirehub_employees')/Attributes`,
        column,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
          }
        }
      );
      
      if (response.status === 200 || response.status === 201 || response.status === 204) {
        this.logger.log(`Successfully created boolean column '${columnName}'.`);
        
        // Publish the customizations
        await this.publishCustomizations();
        
        // Wait for propagation
        this.logger.log('Waiting for column creation to propagate...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error(`Error creating boolean column '${columnName}':`, error.message);
      if (error.response && error.response.data) {
        this.logger.error('Error response:', JSON.stringify(error.response.data, null, 2));
      }
      return false;
    }
  }

  /**
   * Publish all customizations to make new columns available
   * @returns Promise<boolean> True if the customizations were published successfully
   */
  async publishCustomizations(): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();
      
      this.logger.log('Publishing all customizations...');
      
      const response = await axios.post(
        `${this.baseUrl}/PublishAllXml`,
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
          }
        }
      );
      
      if (response.status === 200 || response.status === 204) {
        this.logger.log('Successfully published all customizations.');
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error('Error publishing customizations:', error.message);
      if (error.response && error.response.data) {
        this.logger.error('Error response:', JSON.stringify(error.response.data, null, 2));
      }
      return false;
    }
  }

  /**
   * Ensure all required columns exist in the employee table
   * @returns Promise<void>
   */
  async ensureRequiredColumnsExist(): Promise<void> {
    try {
      // Check if the needsM365 column exists
      const needsM365Exists = await this.columnExists('hirehub_needsm365');
      
      if (!needsM365Exists) {
        this.logger.warn('The hirehub_needsm365 column does not exist. Creating it...');
        
        // Create the needsM365 column
        await this.createBooleanColumn(
          'hirehub_needsm365',
          'Needs M365',
          'Whether the employee needs an M365 license',
          true
        );
      }
      
      // Add checks for other required columns here
      // For example:
      // const startDateExists = await this.columnExists('hirehub_startdate');
      // if (!startDateExists) {
      //   // Create the startDate column
      // }
    } catch (error) {
      this.logger.error('Error ensuring required columns exist:', error.message);
    }
  }
}






















