
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useOnboarding } from '@/context/OnboardingContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserRound, MonitorSmartphone, ShieldCheck } from 'lucide-react';

const RoleSelector: React.FC = () => {
  const { setCurrentRole, setIsAuthenticated } = useOnboarding();
  const navigate = useNavigate();
  
  const handleRoleSelect = (role: 'hr' | 'it' | 'admin') => {
    setCurrentRole(role);
    setIsAuthenticated(true);
    navigate(`/onboarding/${role}`, { replace: false });
  };

  return (
    <div className="max-w-3xl mx-auto px-4 py-12">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-2">Welcome to the Onboarding Platform</h1>
        <p className="text-muted-foreground">Select your role to continue</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6 flex flex-col items-center text-center" onClick={() => handleRoleSelect('hr')}>
            <div className="bg-primary/10 p-3 rounded-full mb-4">
              <UserRound className="h-8 w-8 text-primary" />
            </div>
            <h2 className="font-semibold text-xl mb-2">HR Manager</h2>
            <p className="text-sm text-muted-foreground mb-4">Submit new employee onboarding forms and track progress</p>
            <Button className="mt-auto" onClick={() => handleRoleSelect('hr')}>
              Login as HR
            </Button>
          </CardContent>
        </Card>
        
        <Card className="border hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6 flex flex-col items-center text-center" onClick={() => handleRoleSelect('it')}>
            <div className="bg-primary/10 p-3 rounded-full mb-4">
              <MonitorSmartphone className="h-8 w-8 text-primary" />
            </div>
            <h2 className="font-semibold text-xl mb-2">IT Specialist</h2>
            <p className="text-sm text-muted-foreground mb-4">Process new hire IT requests and run automation tasks</p>
            <Button className="mt-auto" onClick={() => handleRoleSelect('it')}>
              Login as IT
            </Button>
          </CardContent>
        </Card>
        
        <Card className="border hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6 flex flex-col items-center text-center" onClick={() => handleRoleSelect('admin')}>
            <div className="bg-primary/10 p-3 rounded-full mb-4">
              <ShieldCheck className="h-8 w-8 text-primary" />
            </div>
            <h2 className="font-semibold text-xl mb-2">Administrator</h2>
            <p className="text-sm text-muted-foreground mb-4">Full access to manage all onboarding processes and settings</p>
            <Button className="mt-auto" onClick={() => handleRoleSelect('admin')}>
              Login as Admin
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RoleSelector;

