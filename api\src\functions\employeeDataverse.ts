import { app, HttpRequest, HttpResponseInit, InvocationContext } from "@azure/functions";
import { DataverseClient, EmployeeRecord } from "../utils/dataverseClient";
import { validateEmployee } from "../utils/validation";
import { ConfidentialClientApplication } from "@azure/msal-node";

// Hardcoded configuration (temporary solution)
const config = {
  authorityHost: "https://login.microsoftonline.com",
  clientId: "f2c04aea-776a-4ec1-920e-8aca1807c8a8",
  clientSecret: "****************************************",
  tenantId: "a05add7d-79af-43fb-be47-f45d746eaaed",
  dataverse: {
    environmentUrl: "https://scllcdefault.crm.dynamics.com",
    apiVersion: "v9.2",
    environmentId: "Default-a05add7d-79af-43fb-be47-f45d746eaaed"
  }
};

// Define the Dataverse scope constants
// Note: We're now using .default scope with client credentials flow instead of user_impersonation

export async function createEmployee(
  req: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  context.log("Create employee function processing a request");

  const accessToken: string = req.headers.get("Authorization")?.replace("Bearer ", "").trim();
  if (!accessToken) {
    return {
      status: 401,
      jsonBody: {
        error: "No access token was found in request header",
      },
    };
  }

  try {
    const requestBody = await req.json() as Partial<EmployeeRecord>;

    const employeeData: EmployeeRecord = {
      firstName: requestBody.firstName || '',
      lastName: requestBody.lastName || '',
      email: requestBody.email || '',
      ...requestBody
    };

    const validationResult = validateEmployee(employeeData);
    if (!validationResult.valid) {
      return {
        status: 400,
        jsonBody: {
          error: "Validation failed",
          validationErrors: validationResult.errors
        }
      };
    }

    // Use MSAL directly with client secret - using client credentials flow instead of OBO
    try {
      const msalConfig = {
        auth: {
          clientId: config.clientId,
          clientSecret: config.clientSecret,
          authority: `${config.authorityHost}/${config.tenantId}`,
        },
        system: {
          loggerOptions: {
            loggerCallback: (level: number, message: string, containsPii: boolean) => {
              if (!containsPii) context.log(`MSAL (${level}): ${message}`);
            },
            piiLoggingEnabled: false,
            logLevel: 3 // Info level
          }
        }
      };

      context.log("Creating MSAL client with explicit client secret auth");
      const msalClient = new ConfidentialClientApplication(msalConfig);

      // Use client credentials flow instead of OBO
      const clientCredentialRequest = {
        scopes: [`https://orgbce2d577.crm.dynamics.com/.default`],
        skipCache: true // Force token acquisition from server
      };

      context.log("Requesting token with client credentials flow");
      const result = await msalClient.acquireTokenByClientCredential(clientCredentialRequest);

      if (result && result.accessToken) {
        context.log("Successfully acquired token using client credentials flow");

        // Create a custom credential that implements the TokenCredential interface
        const customCredential = {
          getToken: async () => {
            // Return an AccessToken object with the required expiresOnTimestamp property
            return {
              token: result.accessToken,
              expiresOnTimestamp: result.expiresOn?.getTime() || Date.now() + 3600 * 1000 // Default to 1 hour from now if no expiration
            };
          }
        };

        const dataverseClient = new DataverseClient(customCredential, context);

        try {
          await dataverseClient.ensureEmployeeTableExists();
        } catch (tableError) {
          context.error("Error ensuring Dataverse table exists:", tableError);
          // Continue despite table creation error
        }

        // Add user information from the access token if possible
        try {
          // Decode the JWT token to get user information
          const tokenParts = accessToken.split('.');
          if (tokenParts.length === 3) {
            const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
            if (payload.upn) {
              employeeData.submittedBy = payload.upn;
            } else if (payload.email) {
              employeeData.submittedBy = payload.email;
            } else if (payload.name) {
              employeeData.submittedBy = payload.name;
            }
          }
        } catch (tokenError) {
          context.log("Could not extract user info from token:", tokenError);
          // Continue without user info
        }

        employeeData.submittedAt = new Date().toISOString();
        employeeData.status = 'submitted';

        const dataverseResponse = await dataverseClient.createEmployee(employeeData);

        return {
          status: 201,
          jsonBody: {
            message: "Employee record created successfully",
            employee: dataverseResponse
          }
        };
      } else {
        throw new Error("Failed to acquire token using client credentials");
      }
    } catch (error) {
      context.error("Error in MSAL flow:", error);

      return {
        status: 500,
        jsonBody: {
          error: "Failed to authenticate with Dataverse",
          details: error.message || "Unknown error",
          errorCode: error.errorCode || error.code || 'unknown'
        }
      };
    }
  } catch (error) {
    context.error("Error creating employee record:", error);

    return {
      status: 500,
      jsonBody: {
        error: "Failed to create employee record in Dataverse",
        details: error.message,
        errorCode: error.code || 'unknown'
      }
    };
  }
}

export async function getEmployees(
  req: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  context.log("Get employees function processing a request");

  const accessToken: string = req.headers.get("Authorization")?.replace("Bearer ", "").trim();
  if (!accessToken) {
    return {
      status: 401,
      jsonBody: {
        error: "No access token was found in request header",
      },
    };
  }

  try {
    // Log token details (but not the full token)
    context.log("Access token first 20 chars:", accessToken.substring(0, 20) + "...");

    // Use MSAL directly with client secret - using client credentials flow instead of OBO
    try {
      const msalConfig = {
        auth: {
          clientId: config.clientId,
          clientSecret: config.clientSecret,
          authority: `${config.authorityHost}/${config.tenantId}`,
        },
        system: {
          loggerOptions: {
            loggerCallback: (level: number, message: string, containsPii: boolean) => {
              if (!containsPii) context.log(`MSAL (${level}): ${message}`);
            },
            piiLoggingEnabled: false,
            logLevel: 3 // Info level
          }
        }
      };

      context.log("Creating MSAL client with explicit client secret auth");
      const msalClient = new ConfidentialClientApplication(msalConfig);

      // Use client credentials flow instead of OBO
      const clientCredentialRequest = {
        scopes: [`https://orgbce2d577.crm.dynamics.com/.default`],
        skipCache: true // Force token acquisition from server
      };

      context.log("Requesting token with client credentials flow");
      const result = await msalClient.acquireTokenByClientCredential(clientCredentialRequest);

      if (result && result.accessToken) {
        context.log("Successfully acquired token using client credentials flow");

        // Create a custom credential that implements the TokenCredential interface
        const customCredential = {
          getToken: async () => {
            // Return an AccessToken object with the required expiresOnTimestamp property
            return {
              token: result.accessToken,
              expiresOnTimestamp: result.expiresOn?.getTime() || Date.now() + 3600 * 1000 // Default to 1 hour from now if no expiration
            };
          }
        };

        const dataverseClient = new DataverseClient(customCredential, context);

        try {
          await dataverseClient.ensureEmployeeTableExists();
        } catch (tableError) {
          context.error("Error ensuring Dataverse table exists:", tableError);
          // Continue despite table creation error
        }

        const employees = await dataverseClient.getEmployees();

        return {
          status: 200,
          jsonBody: {
            employees: employees
          }
        };
      } else {
        throw new Error("Failed to acquire token using client credentials");
      }
    } catch (error) {
      context.error("Error in MSAL flow:", error);

      return {
        status: 500,
        jsonBody: {
          error: "Failed to authenticate with Dataverse",
          details: error.message || "Unknown error",
          errorCode: error.errorCode || error.code || 'unknown'
        }
      };
    }
  } catch (error) {
    context.error("Error retrieving employee records:", error);
    return {
      status: 500,
      jsonBody: {
        error: "Failed to retrieve employee records from Dataverse",
        details: error.message
      }
    };
  }
}

export async function getEmployeeById(
  req: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  context.log("Get employee by ID function processing a request");

  const accessToken: string = req.headers.get("Authorization")?.replace("Bearer ", "").trim();
  if (!accessToken) {
    return {
      status: 401,
      jsonBody: {
        error: "No access token was found in request header",
      },
    };
  }

  const employeeId = req.params.id;
  if (!employeeId) {
    return {
      status: 400,
      jsonBody: {
        error: "Employee ID is required"
      }
    };
  }

  try {
    // Log token details (but not the full token)
    context.log("Access token first 20 chars:", accessToken.substring(0, 20) + "...");

    // Use MSAL directly with client secret - using client credentials flow instead of OBO
    try {
      const msalConfig = {
        auth: {
          clientId: config.clientId,
          clientSecret: config.clientSecret,
          authority: `${config.authorityHost}/${config.tenantId}`,
        },
        system: {
          loggerOptions: {
            loggerCallback: (level: number, message: string, containsPii: boolean) => {
              if (!containsPii) context.log(`MSAL (${level}): ${message}`);
            },
            piiLoggingEnabled: false,
            logLevel: 3 // Info level
          }
        }
      };

      context.log("Creating MSAL client with explicit client secret auth");
      const msalClient = new ConfidentialClientApplication(msalConfig);

      // Use client credentials flow instead of OBO
      const clientCredentialRequest = {
        scopes: [`https://orgbce2d577.crm.dynamics.com/.default`],
        skipCache: true // Force token acquisition from server
      };

      context.log("Requesting token with client credentials flow");
      const result = await msalClient.acquireTokenByClientCredential(clientCredentialRequest);

      if (result && result.accessToken) {
        context.log("Successfully acquired token using client credentials flow");

        // Create a custom credential that implements the TokenCredential interface
        const customCredential = {
          getToken: async () => {
            // Return an AccessToken object with the required expiresOnTimestamp property
            return {
              token: result.accessToken,
              expiresOnTimestamp: result.expiresOn?.getTime() || Date.now() + 3600 * 1000 // Default to 1 hour from now if no expiration
            };
          }
        };

        const dataverseClient = new DataverseClient(customCredential, context);

        try {
          await dataverseClient.ensureEmployeeTableExists();
        } catch (tableError) {
          context.error("Error ensuring Dataverse table exists:", tableError);
          // Continue despite table creation error
        }

        const employee = await dataverseClient.getEmployeeById(employeeId);

        if (!employee) {
          return {
            status: 404,
            jsonBody: {
              error: `Employee with ID ${employeeId} not found`
            }
          };
        }

        return {
          status: 200,
          jsonBody: {
            employee: employee
          }
        };
      } else {
        throw new Error("Failed to acquire token using client credentials");
      }
    } catch (error) {
      context.error("Error in MSAL flow:", error);

      return {
        status: 500,
        jsonBody: {
          error: "Failed to authenticate with Dataverse",
          details: error.message || "Unknown error",
          errorCode: error.errorCode || error.code || 'unknown'
        }
      };
    }
  } catch (error) {
    context.error(`Error retrieving employee record with ID ${employeeId}:`, error);
    return {
      status: 500,
      jsonBody: {
        error: `Failed to retrieve employee record with ID ${employeeId} from Dataverse`,
        details: error.message
      }
    };
  }
}

export async function testAuth(
  _req: HttpRequest, // Prefix with underscore to indicate it's intentionally unused
  context: InvocationContext
): Promise<HttpResponseInit> {
  context.log("Test auth function processing a request");

  try {
    // Log environment variables (safely)
    const envVars = {
      M365_CLIENT_ID_exists: !!process.env.M365_CLIENT_ID,
      M365_CLIENT_ID_prefix: process.env.M365_CLIENT_ID?.substring(0, 5) + "...",
      M365_CLIENT_ID_length: process.env.M365_CLIENT_ID?.length,
      M365_CLIENT_SECRET_exists: !!process.env.M365_CLIENT_SECRET,
      M365_CLIENT_SECRET_length: process.env.M365_CLIENT_SECRET?.length,
      M365_TENANT_ID_exists: !!process.env.M365_TENANT_ID,
      M365_TENANT_ID_prefix: process.env.M365_TENANT_ID?.substring(0, 5) + "...",
      M365_AUTHORITY_HOST: process.env.M365_AUTHORITY_HOST,
      DATAVERSE_ENVIRONMENT_URL: process.env.DATAVERSE_ENVIRONMENT_URL,
      DATAVERSE_ORG_NAME: process.env.DATAVERSE_ORG_NAME,
      DATAVERSE_API_VERSION: process.env.DATAVERSE_API_VERSION
    };

    // Log hardcoded config values (safely)
    const configValues = {
      authorityHost: config.authorityHost,
      clientId_prefix: config.clientId?.substring(0, 5) + "...",
      clientId_length: config.clientId?.length,
      clientSecret_exists: !!config.clientSecret,
      clientSecret_length: config.clientSecret?.length,
      tenantId_prefix: config.tenantId?.substring(0, 5) + "...",
      dataverse_environmentUrl: config.dataverse.environmentUrl,
      dataverse_apiVersion: config.dataverse.apiVersion
    };

    return {
      status: 200,
      jsonBody: {
        message: "Auth test completed",
        environmentVariables: envVars,
        configValues: configValues
      }
    };
  } catch (error) {
    context.error("Error in test auth function:", error);
    return {
      status: 500,
      jsonBody: {
        error: "Failed to test auth configuration",
        details: error.message
      }
    };
  }
}

// Register HTTP functions
app.http("createEmployee", {
  methods: ["POST"],
  authLevel: "anonymous",
  handler: createEmployee,
});

app.http("getEmployees", {
  methods: ["GET"],
  authLevel: "anonymous",
  handler: getEmployees,
});

app.http("getEmployeeById", {
  methods: ["GET"],
  authLevel: "anonymous",
  route: "employee/{id}",
  handler: getEmployeeById,
});

app.http("testAuth", {
  methods: ["GET"],
  authLevel: "anonymous",
  handler: testAuth,
});










