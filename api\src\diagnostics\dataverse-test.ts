/**
 * Dataverse API Diagnostic Script
 *
 * This script tests various aspects of the Dataverse API connection and table creation
 * to help identify where issues are occurring.
 */

import axios from 'axios';
import { ClientSecretCredential } from '@azure/identity';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const config = {
  dataverse: {
    environmentUrl: "https://orgbce2d577.crm.dynamics.com",
    apiVersion: "v9.2"
  },
  auth: {
    tenantId: process.env.TENANT_ID || '',
    clientId: process.env.CLIENT_ID || '',
    clientSecret: process.env.CLIENT_SECRET || ''
  }
};

// Create a credential using client secret
const credential = new ClientSecretCredential(
  config.auth.tenantId,
  config.auth.clientId,
  config.auth.clientSecret
);

// Logger
const logger = {
  log: (message: string, ...args: any[]) => {
    console.log(`[${new Date().toISOString()}] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`, ...args);
  }
};

/**
 * Test getting an access token
 */
async function testGetAccessToken(): Promise<string | null> {
  try {
    logger.log('Testing token acquisition...');

    const resources = [
      config.dataverse.environmentUrl,
      `${config.dataverse.environmentUrl}/.default`,
      `${config.dataverse.environmentUrl}/user_impersonation`
    ];

    for (const resource of resources) {
      try {
        logger.log(`Requesting token for resource: ${resource}`);
        const tokenResponse = await credential.getToken(resource);

        if (tokenResponse && tokenResponse.token) {
          logger.log(`✅ Successfully acquired token for resource: ${resource}`);

          const tokenParts = tokenResponse.token.split('.');
          if (tokenParts.length >= 2) {
            try {
              const header = JSON.parse(Buffer.from(tokenParts[0], 'base64').toString());
              logger.log(`Token header: ${JSON.stringify(header, null, 2)}`);

              const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
              logger.log(`Token payload (partial): ${JSON.stringify({
                aud: payload.aud,
                iss: payload.iss,
                exp: payload.exp,
                appid: payload.appid,
                tid: payload.tid
              }, null, 2)}`);
            } catch (e) {
              logger.error('Error parsing token:', e);
            }
          }

          return tokenResponse.token;
        }
      } catch (error) {
        logger.error(`❌ Failed to get token for resource ${resource}:`, error);
      }
    }

    logger.error('❌ All token acquisition attempts failed');
    return null;
  } catch (error) {
    logger.error('❌ Error in testGetAccessToken:', error);
    return null;
  }
}

/**
 * Test basic API connectivity
 */
async function testApiConnectivity(accessToken: string): Promise<boolean> {
  try {
    logger.log('Testing basic API connectivity...');

    const endpoints = [
      `${config.dataverse.environmentUrl}/api/data/${config.dataverse.apiVersion}/`,
      `${config.dataverse.environmentUrl}/api/data/${config.dataverse.apiVersion}/EntityDefinitions?$top=1`
    ];

    for (const endpoint of endpoints) {
      try {
        logger.log(`Testing endpoint: ${endpoint}`);

        const response = await axios.get(endpoint, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: 'application/json',
            'OData-MaxVersion': '4.0',
            'OData-Version': '4.0'
          }
        });

        logger.log(`✅ Successfully connected to endpoint: ${endpoint}`);
        logger.log(`Response status: ${response.status}`);

        if (response.data) {
          if (typeof response.data === 'object') {
            logger.log(`Response data (sample): ${JSON.stringify(response.data, null, 2).substring(0, 500)}...`);
          } else {
            logger.log(`Response data: ${response.data}`);
          }
        }

        return true;
      } catch (error: any) {
        logger.error(`❌ Failed to connect to endpoint ${endpoint}:`, error.message);
        if (error.response) {
          logger.error(`Status: ${error.response.status}`);
          logger.error(`Headers: ${JSON.stringify(error.response.headers, null, 2)}`);
          if (error.response.data) {
            logger.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
          }
        }
      }
    }

    logger.error('❌ All API connectivity tests failed');
    return false;
  } catch (error) {
    logger.error('❌ Error in testApiConnectivity:', error);
    return false;
  }
}

/**
 * Test checking if a table exists
 */
async function testTableExists(accessToken: string, tableName: string): Promise<boolean> {
  try {
    logger.log(`Testing if table '${tableName}' exists...`);
    const url = `${config.dataverse.environmentUrl}/api/data/${config.dataverse.apiVersion}/EntityDefinitions(LogicalName='${tableName}')`;

    try {
      logger.log(`Checking table at: ${url}`);

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Accept: 'application/json',
          'OData-MaxVersion': '4.0',
          'OData-Version': '4.0'
        }
      });

      logger.log(`✅ Table '${tableName}' exists. Status: ${response.status}`);
      return true;
    } catch (error: any) {
      if (error.response && error.response.status === 404) {
        logger.log(`Table '${tableName}' does not exist (404 response)`);
        return false;
      }

      logger.error(`❌ Error checking if table '${tableName}' exists:`, error.message);
      if (error.response) {
        logger.error(`Status: ${error.response.status}`);
        if (error.response.data) {
          logger.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
        }
      }

      return false;
    }
  } catch (error) {
    logger.error(`❌ Error in testTableExists for '${tableName}':`, error);
    return false;
  }
}

/**
 * Test creating a table with minimal entity definition
 */
async function testCreateTable(
  accessToken: string,
  tableName: string,
  displayName: string = 'Test Entity',
  displayCollectionName: string = 'Test Entities',
  description: string = 'Test entity for diagnostic purposes'
): Promise<boolean> {
  try {
    logger.log(`Testing creation of table '${tableName}'...`);

    const entityDefinition = {
      "@odata.type": "Microsoft.Dynamics.CRM.EntityMetadata",
      SchemaName: tableName,
      DisplayName: {
        LocalizedLabels: [{ Label: displayName, LanguageCode: 1033 }]
      },
      DisplayCollectionName: {
        LocalizedLabels: [{ Label: displayCollectionName, LanguageCode: 1033 }]
      },
      Description: {
        LocalizedLabels: [{ Label: description, LanguageCode: 1033 }]
      },
      OwnershipType: 'UserOwned',
      HasActivities: false,
      IsActivity: false,
      HasNotes: false,
      Attributes: [
        {
          "@odata.type": "Microsoft.Dynamics.CRM.StringAttributeMetadata",
          AttributeType: "String",
          AttributeTypeName: { Value: "StringType" },
          SchemaName: `${tableName}_name`,
          DisplayName: {
            LocalizedLabels: [{ Label: "Name", LanguageCode: 1033 }]
          },
          Description: {
            LocalizedLabels: [{ Label: "Type the name of the entity", LanguageCode: 1033 }]
          },
          IsPrimaryName: true,
          RequiredLevel: { Value: "ApplicationRequired" },
          MaxLength: 100,
          FormatName: { Value: "Text" }
        }
      ]
    };

    logger.log(`Entity definition: ${JSON.stringify(entityDefinition, null, 2)}`);

    const url = `${config.dataverse.environmentUrl}/api/data/${config.dataverse.apiVersion}/EntityDefinitions`;

    const response = await axios.post(url, entityDefinition, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'OData-MaxVersion': '4.0',
        'OData-Version': '4.0'
      }
    });

    logger.log(`✅ Table '${tableName}' created successfully. Status: ${response.status}`);
    if (response.data) {
      logger.log(`Response data: ${JSON.stringify(response.data, null, 2)}`);
    }

    return true;
  } catch (error: any) {
    logger.error(`❌ Failed to create table '${tableName}':`, error.message);
    if (error.response) {
      logger.error(`Status: ${error.response.status}`);
      if (error.response.data) {
        logger.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
      }
    }

    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  try {
    logger.log('Starting Dataverse API diagnostic tests...');

    const accessToken = await testGetAccessToken();
    if (!accessToken) {
      logger.error('❌ Failed to get access token. Cannot proceed with further tests.');
      return;
    }

    const apiConnectivity = await testApiConnectivity(accessToken);
    if (!apiConnectivity) {
      logger.error('❌ Failed to connect to Dataverse API. Cannot proceed with further tests.');
      return;
    }

    const testTableName = 'hirehub_test';
    const tableExists = await testTableExists(accessToken, testTableName);

    if (!tableExists) {
      logger.log(`Table '${testTableName}' does not exist. Attempting to create it...`);
      const created = await testCreateTable(accessToken, testTableName);
      if (!created) {
        logger.error(`❌ Failed to create table '${testTableName}'`);
        return;
      }
    } else {
      logger.log(`Table '${testTableName}' already exists. Skipping creation test.`);
    }

    const actualTableName = 'hirehub_employees';
    const actualTableExists = await testTableExists(accessToken, actualTableName);

    if (actualTableExists) {
      logger.log(`✅ Table '${actualTableName}' exists.`);
    } else {
      logger.log(`Table '${actualTableName}' does not exist. Attempting to create it...`);

      // Create with more appropriate names for the employee table
      const employeeTableCreated = await testCreateTable(
        accessToken,
        actualTableName,
        'Employee',
        'Employees',
        'Table to store employee information for HireHub'
      );

      if (employeeTableCreated) {
        logger.log(`✅ Successfully created table '${actualTableName}'`);
      } else {
        logger.error(`❌ Failed to create table '${actualTableName}'`);
      }
    }

    logger.log('✅ All diagnostics completed.');
  } catch (error) {
    logger.error('❌ Unexpected error in runTests:', error);
  }
}

// Run it
runTests();
