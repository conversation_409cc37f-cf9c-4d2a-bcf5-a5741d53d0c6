/**
 * Utility functions for Microsoft Teams initialization
 */

import * as microsoftTeams from "@microsoft/teams-js";

/**
 * Initialize the Microsoft Teams SDK
 * @returns Promise<void>
 */
export async function initializeTeamsSDK(): Promise<void> {
  try {
    console.log('Teams SDK: Starting initialization');
    // Check if Teams SDK is available
    if (microsoftTeams) {
      console.log('Teams SDK: SDK is available, calling app.initialize()');
      // Initialize the Teams SDK
      await microsoftTeams.app.initialize();
      console.log('Teams SDK: Initialization successful');
      
      // Get context to verify we're properly initialized
      try {
        const context = await microsoftTeams.app.getContext();
        console.log('Teams SDK: Context retrieved successfully', {
          hostClientType: context.app.host.clientType,
          sessionId: context.app.sessionId,
          theme: context.app.theme
        });
      } catch (contextError) {
        console.error('Teams SDK: Failed to get context after initialization', contextError);
      }
    } else {
      console.warn('Teams SDK: Microsoft Teams SDK not available');
    }
  } catch (error) {
    console.error('Teams SDK: Error during initialization:', error);
    console.error('Teams SDK: Error details:', {
      message: error?.message,
      stack: error?.stack,
      name: error?.name
    });
  }
}

/**
 * Check if the app is running in Teams
 * @returns boolean True if running in Teams, false otherwise
 */
export function isRunningInTeams(): boolean {
  return window.parent !== window.self;
}

/**
 * Get the Teams context
 * @returns Promise<microsoftTeams.app.Context> Teams context
 */
export async function getTeamsContext(): Promise<microsoftTeams.app.Context> {
  try {
    return await microsoftTeams.app.getContext();
  } catch (error) {
    console.error("Error getting Teams context:", error);
    throw error;
  }
}

