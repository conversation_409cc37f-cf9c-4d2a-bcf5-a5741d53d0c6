
import React from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Toggle } from '@/components/ui/toggle';
import { 
  User, 
  FileCheck, 
  Smartphone, 
  Users, 
  MessageSquare,
  UserCheck,
  FolderGit2,
  FileEdit
} from 'lucide-react';

const ProjectSetupConfigurator: React.FC = () => {
  const { projectConfig, updateProjectConfig, setConfigCompleted } = useOnboarding();

  const configOptions = [
    {
      id: 'useEntraID',
      name: 'Microsoft Entra ID',
      description: 'Create and manage user identities',
      icon: <User className="h-5 w-5" />,
      enabled: true, // Always needed
      disabled: true
    },
    {
      id: 'useM365',
      name: 'Microsoft 365 Licensing',
      description: 'Assign licenses for Office apps and services',
      icon: <FileCheck className="h-5 w-5" />,
      enabled: projectConfig.useM365,
      disabled: false
    },
    {
      id: 'useIntune',
      name: 'Microsoft Intune',
      description: 'Provision and manage employee devices',
      icon: <Smartphone className="h-5 w-5" />,
      enabled: projectConfig.useIntune,
      disabled: false
    },
    {
      id: 'useTeams',
      name: 'Microsoft Teams',
      description: 'Add users to teams and channels',
      icon: <Users className="h-5 w-5" />,
      enabled: projectConfig.useTeams,
      disabled: false
    },
    {
      id: 'useSlack',
      name: 'Slack Integration',
      description: 'Add users to Slack workspaces',
      icon: <MessageSquare className="h-5 w-5" />,
      enabled: projectConfig.useSlack,
      disabled: false
    },
    {
      id: 'useHRSystem',
      name: 'HR System Integration',
      description: 'Connect to your HR system',
      icon: <UserCheck className="h-5 w-5" />,
      enabled: projectConfig.useHRSystem, 
      disabled: false
    },
    {
      id: 'useDynamicGroups',
      name: 'Dynamic Groups',
      description: 'Auto-assign users to groups',
      icon: <FolderGit2 className="h-5 w-5" />,
      enabled: projectConfig.useDynamicGroups,
      disabled: false
    },
    {
      id: 'useCustomFields',
      name: 'Custom Fields',
      description: 'Add organization-specific fields',
      icon: <FileEdit className="h-5 w-5" />,
      enabled: projectConfig.useCustomFields,
      disabled: false
    }
  ];

  const handleToggle = (id: string) => {
    updateProjectConfig(id as keyof typeof projectConfig, !projectConfig[id as keyof typeof projectConfig]);
  };

  const handleContinue = () => {
    setConfigCompleted(true);
  };

  return (
    <div className="max-w-3xl mx-auto px-4 pb-16 animate-fade-in">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-2">Configure Your Onboarding Flow</h2>
        <p className="text-muted-foreground text-lg">
          Select which systems and features you want to include in your employee onboarding process
        </p>
      </div>

      <Card className="shadow-lg border-0 mb-8">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {configOptions.map((option) => (
              <div 
                key={option.id}
                className={`flex items-start p-4 rounded-lg border ${
                  option.enabled ? 'bg-primary/5 border-primary/20' : 'bg-background border-muted'
                }`}
              >
                <div className={`rounded-full p-2 mr-4 ${
                  option.enabled ? 'bg-primary/10 text-primary' : 'bg-muted text-muted-foreground'
                }`}>
                  {option.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{option.name}</h3>
                    <Toggle
                      pressed={option.enabled}
                      onPressedChange={() => handleToggle(option.id)}
                      disabled={option.disabled}
                      aria-label={`Toggle ${option.name}`}
                      className={option.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{option.description}</p>
                  {option.disabled && (
                    <p className="text-xs text-primary/80 mt-2">Required for basic onboarding</p>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 flex justify-center">
            <Button size="lg" onClick={handleContinue}>
              Continue to Onboarding Demo
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <div className="text-center text-sm text-muted-foreground">
        <p>This configurator simulates how your onboarding process could work with your selected systems.</p>
        <p>The actual implementation would connect to your Microsoft 365 tenant and other selected systems.</p>
      </div>
    </div>
  );
};

export default ProjectSetupConfigurator;
