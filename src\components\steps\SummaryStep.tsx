import React from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Card, CardContent } from '@/components/ui/card';
import { User, Briefcase, Laptop, ClipboardList } from 'lucide-react';

const SummaryStep: React.FC = () => {
  const { formData, currentRole } = useOnboarding();

  const SectionHeader = ({ icon: Icon, title }: { icon: any, title: string }) => (
    <div className="flex items-center gap-2 mb-6 pb-2 border-b">
      <Icon className="h-5 w-5 text-muted-foreground" />
      <h3 className="font-semibold text-lg">{title}</h3>
    </div>
  );

  const InfoItem = ({ label, value }: { label: string, value: string | null }) => (
    <div className="py-3">
      <div className="text-sm font-medium text-muted-foreground mb-1">{label}</div>
      <div className="text-sm">{value || '—'}</div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-10">
        <h2 className="text-2xl font-semibold mb-2">Review & Confirm</h2>
        <p className="text-muted-foreground">
          Please review all information before proceeding
        </p>
      </div>

      <div className="space-y-8">
        <Card className="shadow-sm">
          <CardContent className="pt-6">
            <SectionHeader icon={User} title="Personal Information" />
            <div className="grid grid-cols-2 gap-x-12 gap-y-1">
              <InfoItem label="First Name" value={formData.firstName} />
              <InfoItem label="Last Name" value={formData.lastName} />
              <InfoItem label="Email Address" value={formData.email} />
              <InfoItem label="Phone Number" value={formData.phoneNumber} />
              <InfoItem 
                label="Home Address" 
                value={`${formData.address}, ${formData.city}, ${formData.state} ${formData.zip}`} 
              />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardContent className="pt-6">
            <SectionHeader icon={Briefcase} title="Job Details" />
            <div className="grid grid-cols-2 gap-x-12 gap-y-1">
              <InfoItem label="Job Title" value={formData.jobTitle} />
              <InfoItem label="Department" value={formData.department} />
              <InfoItem label="Start Date" value={formData.startDate} />
              <InfoItem label="Work Location" value={formData.physicalWorkLocation} />
              <InfoItem 
                label="Hiring Manager" 
                value={`${formData.hiringManagerFirstName} ${formData.hiringManagerLastName}`} 
              />
              <InfoItem label="Manager Email" value={formData.hiringManagerEmail} />
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardContent className="pt-6">
            <SectionHeader icon={Laptop} title="IT Setup & Equipment" />
            <div className="grid grid-cols-2 gap-x-12 gap-y-1">
              <InfoItem label="Work Email" value={formData.workEmail} />
              <InfoItem label="Device Type" value={formData.deviceType} />
              <InfoItem label="Phone Type" value={formData.phoneType} />
              <InfoItem label="M365 License" value={formData.needsM365 ? 'Yes' : 'No'} />
              <InfoItem 
                label="IT Notes" 
                value={formData.itNotes || 'No additional notes'} 
              />
            </div>
          </CardContent>
        </Card>

        {currentRole === 'hr' && (
          <Card className="shadow-sm">
            <CardContent className="pt-6">
              <SectionHeader icon={ClipboardList} title="HR Information" />
              <div className="grid grid-cols-2 gap-x-12 gap-y-1">
                <InfoItem label="Position Type" value={formData.positionType} />
                <InfoItem label="Hire Type" value={formData.hireType} />
                <InfoItem label="Work Schedule" value={formData.workSchedule} />
                <InfoItem 
                  label="HR Notes" 
                  value={formData.hrNotes || 'No additional notes'} 
                />
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SummaryStep;

