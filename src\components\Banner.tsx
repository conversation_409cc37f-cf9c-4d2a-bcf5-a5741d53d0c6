import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { User } from 'lucide-react';
import defaultLogo from '@/assets/default-logo.png';

interface BannerProps {
  customLogo?: string;
}

const Banner: React.FC<BannerProps> = ({ customLogo }) => {
  const navigate = useNavigate();

  const handleLogoClick = () => {
    navigate('/');
  };

  const handleLoginClick = () => {
    navigate('/login');
  };

  return (
    <nav className="bg-white sticky top-0 z-50 shadow-sm border-b border-brand-navy/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          <div className="flex items-center space-x-4">
            <img
              src={customLogo || defaultLogo}
              alt="Logo"
              className="h-12 w-auto cursor-pointer hover:opacity-90 transition-opacity"
              onClick={handleLogoClick}
            />
            <div className="h-6 w-px bg-brand-navy/10" />
            <span className="text-lg font-semibold text-brand-dark">
              Employee Onboarding
            </span>
          </div>

          <Button
            variant="outline"
            onClick={handleLoginClick}
            className="flex items-center gap-2 border-brand-blue/20 text-brand-blue hover:bg-brand-blue-light hover:text-brand-blue hover:border-brand-blue/30 transition-colors"
          >
            <User className="h-4 w-4" />
            Login
          </Button>
        </div>
      </div>
    </nav>
  );
};

export default Banner;



