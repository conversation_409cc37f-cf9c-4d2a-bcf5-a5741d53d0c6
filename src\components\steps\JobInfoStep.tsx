
import React from 'react';
import { useOnboarding } from '@/context/OnboardingContext';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Briefcase } from 'lucide-react';

const JobInfoStep: React.FC = () => {
  const { formData, updateFormData } = useOnboarding();

  const departments = [
    "Sales",
    "Marketing",
    "Engineering",
    "Product",
    "Customer Support",
    "Operations",
    "Finance",
    "HR",
    "Legal",
    "Executive"
  ];

  return (
    <div className="step-content">
      <div>
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-primary/10 p-2 rounded-full">
            <Briefcase className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold">Job Information</h2>
            <p className="text-muted-foreground">Enter details about the employee's position</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="jobTitle">Job Title</Label>
            <Input 
              id="jobTitle" 
              placeholder="Job Title" 
              value={formData.jobTitle}
              onChange={(e) => updateFormData('jobTitle', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <Select 
              value={formData.department} 
              onValueChange={(value) => updateFormData('department', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map(dept => (
                  <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="manager">Manager</Label>
            <Input 
              id="manager" 
              placeholder="Manager's Name" 
              value={formData.manager}
              onChange={(e) => updateFormData('manager', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="startDate">Start Date</Label>
            <Input 
              id="startDate" 
              type="date" 
              value={formData.startDate}
              onChange={(e) => updateFormData('startDate', e.target.value)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobInfoStep;
