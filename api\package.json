{"name": "teamsfx-template-api", "version": "1.0.0", "engines": {"node": "18 || 20 || 22"}, "main": "dist/src/functions/*.js", "scripts": {"dev:teamsfx": "env-cmd --silent -f .localConfigs npm run dev", "dev": "func start --typescript --language-worker=\"--inspect=9229\" --port \"7071\" --cors \"*\"", "build": "tsc", "watch:teamsfx": "tsc -w", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run clean && npm run build", "start": "npx func start", "test": "echo \"Error: no test specified\" && exit 1", "test:dataverse": "ts-node src/diagnostics/run-tests.ts"}, "dependencies": {"@azure/functions": "^4.0.0", "@azure/identity": "^4.9.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@microsoft/teamsfx": "^3.0.0", "axios": "^1.6.2", "isomorphic-fetch": "^3.0.0"}, "devDependencies": {"@types/node": "^18.x", "dotenv": "^16.5.0", "env-cmd": "^10.1.0", "rimraf": "^5.0.0", "ts-node": "^10.9.2", "typescript": "^4.4.4"}}