# Create Teams app package
Write-Host "Creating Teams app package..." -ForegroundColor Cyan

# Check if the files exist
if (-not (Test-Path -Path "manifest.json")) {
    Write-Host "manifest.json not found in the current directory!" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path -Path "color.png")) {
    Write-Host "color.png not found in the current directory!" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path -Path "outline.png")) {
    Write-Host "outline.png not found in the current directory!" -ForegroundColor Red
    exit 1
}

# Create the zip file
try {
    Compress-Archive -Path manifest.json, color.png, outline.png -DestinationPath HireHub.zip -Force
    Write-Host "Teams app package created successfully: HireHub.zip" -ForegroundColor Green
    
    # Get the full path to the zip file
    $zipPath = (Get-Item -Path "HireHub.zip").FullName
    Write-Host "Full path: $zipPath" -ForegroundColor Green
    
    # Open the folder containing the zip file
    explorer.exe (Split-Path -Parent $zipPath)
} catch {
    Write-Host "Error creating Teams app package: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`nTo install the app in Teams:" -ForegroundColor Yellow
Write-Host "1. Go to Microsoft Teams" -ForegroundColor Yellow
Write-Host "2. Click on Apps in the left sidebar" -ForegroundColor Yellow
Write-Host "3. Click 'Manage your apps' at the bottom" -ForegroundColor Yellow
Write-Host "4. Click 'Upload an app' > 'Upload a custom app'" -ForegroundColor Yellow
Write-Host "5. Select the HireHub.zip file" -ForegroundColor Yellow
