<!--This file is used during the Teams authentication flow to assist with retrieval of the access token.-->
<!--If you're not familiar with this, do not alter or remove this file from your project.-->
<html>
  <head>
    <title>Login End Page</title>
    <meta charset="utf-8" />
  </head>

  <body>
    <script
      src="https://res.cdn.office.net/teams-js/2.31.1/js/MicrosoftTeams.min.js"
      integrity="sha384-ihAqYgEJz9hzEU+HaYodG1aTzjebC/wKXQi1nWKZG7OLAUyOL9ZrzD/SfZu79Jeu"
      crossorigin="anonymous"
    ></script>
    <script
      type="text/javascript"
      src="https://alcdn.msauth.net/browser/2.21.0/js/msal-browser.min.js"
      integrity="sha384-s/NxjjAgw1QgpDhOlVjTceLl4axrp5nqpUbCPOEQy1PqbFit9On6uw2XmEF1eq0s"
      crossorigin="anonymous"
    ></script>
    <script type="text/javascript">
      var currentURL = new URL(window.location);
      var clientId = currentURL.searchParams.get("clientId");

      microsoftTeams.app.initialize().then(() => {
        microsoftTeams.app.getContext().then(async (context) => {
          const msalConfig = {
            auth: {
              clientId: clientId,
              authority: `https://login.microsoftonline.com/${context.tid}`,
              navigateToLoginRequestUrl: false,
            },
            cache: {
              cacheLocation: "sessionStorage",
            },
          };

          const msalInstance = new window.msal.PublicClientApplication(msalConfig);
          msalInstance
            .handleRedirectPromise()
            .then((tokenResponse) => {
              if (tokenResponse !== null) {
                microsoftTeams.authentication.notifySuccess(
                  JSON.stringify({
                    sessionStorage: sessionStorage,
                  })
                );
              } else {
                microsoftTeams.authentication.notifyFailure("Get empty response.");
              }
            })
            .catch((error) => {
              microsoftTeams.authentication.notifyFailure(JSON.stringify(error));
            });
        });
      });
    </script>
  </body>
</html>
