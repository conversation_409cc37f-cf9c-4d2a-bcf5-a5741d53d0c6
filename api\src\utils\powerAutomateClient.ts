/**
 * Utility functions for interacting with Power Automate
 */

import axios from 'axios';
import config from '../config';

/**
 * Send data to Power Automate flow
 * @param data Data to send to Power Automate
 * @returns Promise<any> Response from Power Automate
 */
export async function sendToFlowUrl(data: any): Promise<any> {
  try {
    const flowUrl = process.env.POWER_AUTOMATE_FLOW_URL;
    if (!flowUrl) {
      throw new Error('Power Automate Flow URL is not configured');
    }

    // Send request to Power Automate
    const response = await axios.post(flowUrl, data, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error sending data to Power Automate:', error);
    throw error;
  }
}
