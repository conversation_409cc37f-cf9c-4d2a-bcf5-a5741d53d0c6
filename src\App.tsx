import { Routes, Route } from 'react-router-dom';
import Dashboard from '@/components/Dashboard';
import { useOnboarding } from '@/context/OnboardingContext';
import Index from '@/pages/Index';
import Login from '@/pages/Login';
import NotFound from '@/pages/NotFound';
import './App.css';
import { TestEmployee } from '@/components/TestEmployee';

function App() {
  const { currentRole } = useOnboarding();

  return (
    <div className="min-h-screen bg-background">
      <div className="teams-container animate-fade-in">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/dashboard" element={<Dashboard role={currentRole} />} />
            <Route path="/onboarding/*" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </div>
      </div>
      <TestEmployee />
    </div>
  );
}

export default App;






