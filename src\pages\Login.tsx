import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { login, getUserInfo } from '../utils/authUtils';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [userInfo, setUserInfo] = useState<any>(null);

  useEffect(() => {
    // Check if user is already logged in
    const checkLoginStatus = async () => {
      try {
        setLoading(true);
        const user = await getUserInfo();
        setUserInfo(user);
        console.log('User is already logged in:', user);
      } catch (err) {
        console.log('User is not logged in yet:', err);
      } finally {
        setLoading(false);
      }
    };

    checkLoginStatus();
  }, []);

  const handleLogin = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Login with Graph scopes
      await login('GRAPH');
      
      // Get user info after successful login
      const user = await getUserInfo();
      setUserInfo(user);
      
      console.log('Login successful:', user);
      
      // Navigate to dashboard after successful login
      navigate('/dashboard');
    } catch (err) {
      console.error('Login error:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred during login');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh]">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl text-center">Login to HireHub</CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4 bg-red-50 border-red-200">
              <AlertDescription className="text-red-600">{error}</AlertDescription>
            </Alert>
          )}
          
          {userInfo ? (
            <div className="text-center mb-4">
              <p className="font-medium">Logged in as:</p>
              <p>{userInfo.displayName} ({userInfo.preferredUserName})</p>
              <Button 
                onClick={() => navigate('/dashboard')} 
                className="mt-4 w-full"
              >
                Go to Dashboard
              </Button>
            </div>
          ) : (
            <Button 
              onClick={handleLogin} 
              className="w-full" 
              disabled={loading}
            >
              {loading ? 'Logging in...' : 'Login with Microsoft'}
            </Button>
          )}
          
          <div className="mt-4 text-sm text-center text-gray-500">
            <p>Environment Variables:</p>
            <p>Client ID: {import.meta.env.VITE_CLIENT_ID ? '✓ Set' : '✗ Not Set'}</p>
            <p>Login URL: {import.meta.env.VITE_START_LOGIN_PAGE_URL ? '✓ Set' : '✗ Not Set'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Login;


