# This script helps register an Azure AD app for HireHub with ngrok domain for Teams SSO

# Check if Azure CLI is installed
$azCliInstalled = $null
try {
    $azCliInstalled = Get-Command az -ErrorAction SilentlyContinue
} catch {
    $azCliInstalled = $null
}

if (-not $azCliInstalled) {
    Write-Host "Azure CLI is not installed. Please install it from https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Red
    exit 1
}

# Check if user is logged in to Azure and force re-login
Write-Host "Logging in to Azure..." -ForegroundColor Cyan
az login

# Verify login was successful
$loginStatus = az account show --query name -o tsv 2>$null
if (-not $loginStatus) {
    Write-Host "Failed to log in to Azure. Please try again." -ForegroundColor Red
    exit 1
}

# Get tenant ID
$tenantId = az account show --query tenantId -o tsv
Write-Host "Using tenant ID: $tenantId" -ForegroundColor Green

# Define values
$appName = "HireHub"

# Set ngrok domain
$ngrokDomain = "https://assuring-uniquely-cattle.ngrok-free.app"
Write-Host "Using ngrok domain: $ngrokDomain" -ForegroundColor Green

# Ensure the domain starts with https://
if (-not $ngrokDomain.StartsWith("https://")) {
    $ngrokDomain = "https://" + $ngrokDomain
}

Write-Host "Using domain: $ngrokDomain" -ForegroundColor Green

# Define redirect URIs
$redirectUris = @(
    "$ngrokDomain/auth-start.html",
    "$ngrokDomain/auth.html",
    "$ngrokDomain/dashboard",
    "https://localhost:9000/auth-start.html",
    "https://localhost:9000/auth.html",
    "https://localhost:9000/dashboard"
)

Write-Host "Registering application '$appName' with redirect URIs for ngrok..." -ForegroundColor Cyan

# Create the app registration
try {
    $app = az ad app create `
        --display-name $appName `
        --sign-in-audience "AzureADMyOrg" `
        --web-redirect-uris $redirectUris 2>&1 | Out-String

    if ($app -match "ERROR:") {
        Write-Host "Error creating app registration: $app" -ForegroundColor Red

        # Check if app already exists
        Write-Host "Checking if app '$appName' already exists..." -ForegroundColor Yellow
        $existingApp = az ad app list --display-name $appName --query "[0]" | ConvertFrom-Json

        if ($existingApp) {
            $appId = $existingApp.appId
            Write-Host "Found existing app with ID: $appId" -ForegroundColor Green
        } else {
            Write-Host "Could not find existing app. Please try again or create the app manually." -ForegroundColor Red
            exit 1
        }
    } else {
        $appObj = $app | ConvertFrom-Json
        $appId = $appObj.appId
        Write-Host "Application registered with ID: $appId" -ForegroundColor Green
    }
} catch {
    Write-Host "Error creating app registration: $_" -ForegroundColor Red
    exit 1
}

# Verify we have an app ID
if (-not $appId) {
    Write-Host "Failed to get application ID. Please try again." -ForegroundColor Red
    exit 1
}

# Create a client secret
try {
    # Use a different approach to create the client secret
    Write-Host "Creating client secret..." -ForegroundColor Cyan
    $secretResult = az ad app credential reset --id $appId --display-name "HireHub-Secret" 2>&1

    # Check if the result is a string (error) or an object (success)
    if ($secretResult -is [string] -and $secretResult -match "ERROR:") {
        Write-Host "Error creating client secret: $secretResult" -ForegroundColor Red
        $clientSecret = "dummy-secret-for-local-dev"
        Write-Host "Using dummy client secret for local development" -ForegroundColor Yellow
    } else {
        # Try to parse the JSON result
        try {
            $secret = $secretResult | ConvertFrom-Json
            $clientSecret = $secret.password
            Write-Host "Client secret created successfully" -ForegroundColor Green
        } catch {
            Write-Host "Error parsing client secret response: $_" -ForegroundColor Red
            $clientSecret = "dummy-secret-for-local-dev"
            Write-Host "Using dummy client secret for local development" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "Error creating client secret: $_" -ForegroundColor Red
    $clientSecret = "dummy-secret-for-local-dev"
    Write-Host "Using dummy client secret for local development" -ForegroundColor Yellow
}

# Add Microsoft Graph and Dataverse API permissions
Write-Host "Adding Microsoft Graph and Dataverse API permissions..." -ForegroundColor Cyan

try {
    # Add Microsoft Graph permissions
    $permResult = az ad app permission add --id $appId --api 00000003-0000-0000-c000-000000000000 `
        --api-permissions "741f803b-c850-494e-b5df-cde7c675a1ca=Role" 2>&1 # User.ReadWrite.All

    # Add Dynamics CRM permissions
    $crmPermResult = az ad app permission add --id $appId --api 00000007-0000-0000-c000-000000000000 `
        --api-permissions "78ce3f0f-a1ce-49c2-8cde-64b5c0896db4=Role" 2>&1 # user_impersonation

    if ($permResult -match "ERROR:" -or $crmPermResult -match "ERROR:") {
        Write-Host "Error adding permissions: $permResult $crmPermResult" -ForegroundColor Red
    } else {
        Write-Host "Successfully added Graph and Dataverse permissions" -ForegroundColor Green
    }
} catch {
    Write-Host "Error adding permissions: $_" -ForegroundColor Red
}

# Grant admin consent
Write-Host "Granting admin consent for API permissions..." -ForegroundColor Cyan
Write-Host "Note: This step requires Global Admin privileges" -ForegroundColor Yellow
Write-Host "You need to grant admin consent for:" -ForegroundColor Yellow
Write-Host "1. User.ReadWrite.All (Microsoft Graph)" -ForegroundColor Yellow
Write-Host "2. user_impersonation (Dynamics CRM)" -ForegroundColor Yellow

# Wait a moment for the permission to propagate
Start-Sleep -Seconds 5

try {
    # Try a different approach for admin consent
    $consentUrl = "https://portal.azure.com/#blade/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/CallAnAPI/appId/$appId"
    Write-Host "To grant admin consent, please visit:" -ForegroundColor Yellow
    Write-Host $consentUrl -ForegroundColor Cyan
    Write-Host "Then click on 'Grant admin consent for [your tenant]'" -ForegroundColor Yellow

    # Ask if the user wants to open the URL
    $openBrowser = Read-Host "Do you want to open this URL in your browser? (y/n)"
    if ($openBrowser -eq "y") {
        Start-Process $consentUrl
    }

    # Skip the automated consent since it's likely to fail
    Write-Host "Skipping automated admin consent (requires Global Admin)" -ForegroundColor Yellow
} catch {
    Write-Host "Error with admin consent process: $_" -ForegroundColor Red
    Write-Host "You will need to manually grant admin consent in the Azure portal" -ForegroundColor Yellow
}

# Expose API
# Extract the hostname without protocol
$hostname = $ngrokDomain -replace "https://", "" -replace "http://", ""
# For ngrok, we don't need to include the port in the URI
$appUri = "api://$hostname/$appId"
Write-Host "Setting Application ID URI to $appUri" -ForegroundColor Cyan

try {
    $updateResult = az ad app update --id $appId --identifier-uris $appUri 2>&1

    if ($updateResult -match "ERROR:") {
        Write-Host "Error setting Application ID URI: $updateResult" -ForegroundColor Red
    } else {
        Write-Host "Successfully set Application ID URI" -ForegroundColor Green
    }
} catch {
    Write-Host "Error setting Application ID URI: $_" -ForegroundColor Red
}

# Add scope (access_as_user)
try {
    # First, expose the API with a scope
    Write-Host "Exposing API with access_as_user scope..." -ForegroundColor Cyan

    # Create a JSON file with the scope definition
    $scopeDefinition = @"
    [
      {
        "adminConsentDescription": "Allow the application to access HireHub on behalf of the signed-in user.",
        "adminConsentDisplayName": "Access HireHub",
        "id": "$(New-Guid)",
        "isEnabled": true,
        "type": "User",
        "userConsentDescription": "Allow the application to access HireHub on your behalf.",
        "userConsentDisplayName": "Access HireHub",
        "value": "access_as_user"
      }
    ]
"@

    $scopeFile = "$env:TEMP\scope_definition.json"
    $scopeDefinition | Out-File -FilePath $scopeFile -Encoding utf8

    # Update the app to add the scope
    $updateResult = az ad app update --id $appId --oauth2-permission-scope @$scopeFile 2>&1

    if ($updateResult -match "ERROR:") {
        Write-Host "Error exposing API with scope: $updateResult" -ForegroundColor Red
        Write-Host "You may need to manually add the scope in the Azure portal" -ForegroundColor Yellow
    } else {
        Write-Host "API exposed with scope 'access_as_user'" -ForegroundColor Green
    }

    # Clean up the temporary file
    if (Test-Path $scopeFile) {
        Remove-Item $scopeFile -Force
    }
} catch {
    Write-Host "Error exposing API with scope: $_" -ForegroundColor Red
    Write-Host "You may need to manually add the scope in the Azure portal" -ForegroundColor Yellow
}

# Write .env.local file
$envContent = @"
# Microsoft Entra ID (Azure AD) App Registration details
VITE_CLIENT_ID=$appId
VITE_START_LOGIN_PAGE_URL=$ngrokDomain/auth-start.html

# API endpoint for Azure Functions
VITE_FUNC_ENDPOINT=http://localhost:7071
"@

$envPath = "..\\.env.local"
$envContent | Out-File -FilePath $envPath -Encoding utf8
Write-Host "Created .env.local file with app details" -ForegroundColor Green

# Write Azure Functions local.settings.json
$settingsContent = @"
{
  "IsEncrypted": false,
  "Values": {
    "FUNCTIONS_WORKER_RUNTIME": "node",
    "AzureWebJobsFeatureFlags": "EnableWorkerIndexing",
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",

    "M365_CLIENT_ID": "$appId",
    "M365_CLIENT_SECRET": "$clientSecret",
    "M365_TENANT_ID": "$tenantId",
    "M365_AUTHORITY_HOST": "https://login.microsoftonline.com",

    "DATAVERSE_ENVIRONMENT_URL": "https://yourorg.crm.dynamics.com",
    "DATAVERSE_ORG_NAME": "yourorg",
    "DATAVERSE_API_VERSION": "v9.2"
  },
  "Host": {
    "CORS": "*"
  }
}
"@

$settingsPath = "..\\api\\local.settings.json"
$settingsContent | Out-File -FilePath $settingsPath -Encoding utf8
Write-Host "Created local.settings.json for Azure Functions" -ForegroundColor Green

Write-Host "`nSetup complete!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Update Dataverse config in local.settings.json" -ForegroundColor Yellow
Write-Host "2. Start frontend: npm run dev" -ForegroundColor Yellow
Write-Host "3. Start backend: cd api && npm run dev" -ForegroundColor Yellow
Write-Host "4. Test login: open $ngrokDomain/login in Teams Tab" -ForegroundColor Yellow


