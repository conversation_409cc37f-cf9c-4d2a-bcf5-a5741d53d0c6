
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #3b82f6;
  --primary-foreground: white;
  --secondary: #f1f5f9;
  --secondary-foreground: #1e293b;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #1e293b;
  --card: white;
  --card-foreground: #0f172a;
  --background: white;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  background-color: #f8fafc;
  color: #0f172a;
}

/* Teams-specific styles */
.teams-container {
  padding: 1rem;
  max-width: 100%;
}

/* Card styles */
.card {
  background-color: white;
  color: #0f172a;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Step content */
.step-content {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
}

